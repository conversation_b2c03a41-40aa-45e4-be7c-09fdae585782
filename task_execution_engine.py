"""
Task Execution Engine for Web Crawling System
Orchestrates the execution of crawling tasks with real-time monitoring
"""

import asyncio
import threading
import time
import logging
from typing import Dict, Set, Optional
from urllib.parse import urlparse

from DrissionPage import ChromiumPage

try:
    from improved_forum_crawler import <PERSON>rawlerConfig, FlashSaleDetector, StateManager
except ImportError:
    print("Warning: improved_forum_crawler not found, using basic implementations")
    class CrawlerConfig:
        def __init__(self):
            self.page_load_delay = 3.0
    class FlashSaleDetector:
        def __init__(self, config):
            pass
        def analyze_comment(self, text):
            return "sale" in text.lower() or "offer" in text.lower()
    class StateManager:
        def __init__(self, config):
            pass
        def load_state(self):
            return {}
        def save_state(self, state):
            pass

from task_browser_manager import TaskBrowserManager

# Import TaskManager and TaskStatus with forward reference handling
TaskManager = None
TaskStatus = None

def set_task_manager_classes(tm_class, ts_enum):
    """Set TaskManager and TaskStatus classes to avoid circular imports"""
    global TaskManager, TaskStatus
    TaskManager = tm_class
    TaskStatus = ts_enum


class TaskExecutor:
    """Executes a single crawling task with real-time monitoring"""

    def __init__(self, task_id: str, config: CrawlerConfig,
                 browser_manager: TaskBrowserManager, task_manager):
        self.task_id = task_id
        self.config = config
        self.browser_manager = browser_manager
        self.task_manager = task_manager
        self.logger = logging.getLogger(f"task_executor.{task_id[:8]}")
        
        # Task state
        self.is_running = False
        self.page: Optional[ChromiumPage] = None
        self.flash_sale_detector = FlashSaleDetector(config)
        self.state_manager = StateManager(config)
        
        # Monitoring thread
        self.monitor_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        
        # Statistics
        self.last_comment_count = 0
        self.total_flash_sales_found = 0
        self.last_processed_comment_content = ""
    
    async def start(self):
        """Start the task execution"""
        if self.is_running:
            self.logger.warning(f"Task {self.task_id} is already running")
            return
        
        try:
            # Get task details
            task = self.task_manager.get_task(self.task_id)
            
            # Update status to running
            self.task_manager.update_task_status(self.task_id, "running")
            
            # Create browser tab for this task
            self.page = self.browser_manager.create_task_tab(
                self.task_id, task.post_url, task.forum_domain
            )
            
            # Load task state
            self._load_task_state(task.post_url)
            
            # Start monitoring thread
            self.is_running = True
            self.stop_event.clear()
            
            self.monitor_thread = threading.Thread(
                target=self._monitoring_loop,
                args=(task,),
                name=f"TaskMonitor-{self.task_id[:8]}",
                daemon=True
            )
            self.monitor_thread.start()
            
            self.logger.info(f"Started task {self.task_id} for {task.post_url}")
            
        except Exception as e:
            self.logger.error(f"Failed to start task {self.task_id}: {e}")
            self.task_manager.update_task_status(
                self.task_id, "error", str(e)
            )
            raise
    
    async def stop(self):
        """Stop the task execution"""
        if not self.is_running:
            return
        
        self.logger.info(f"Stopping task {self.task_id}")
        
        # Signal stop
        self.is_running = False
        self.stop_event.set()
        
        # Wait for monitoring thread to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        # Close browser tab
        if self.page:
            self.browser_manager.close_task_tab(self.task_id)
            self.page = None
        
        # Update status
        self.task_manager.update_task_status(self.task_id, "stopped")
        
        self.logger.info(f"Stopped task {self.task_id}")
    
    def _monitoring_loop(self, task):
        """Main monitoring loop for the task"""
        self.logger.info(f"Starting monitoring loop for task {self.task_id}")
        
        while self.is_running and not self.stop_event.is_set():
            try:
                # Perform monitoring cycle
                self._perform_monitoring_cycle(task)
                
                # Wait for next cycle
                if self.stop_event.wait(timeout=task.monitor_interval):
                    break  # Stop event was set
                    
            except Exception as e:
                self.logger.error(f"Error in monitoring loop for task {self.task_id}: {e}")
                self.task_manager.update_task_status(
                    self.task_id, "error", str(e)
                )
                break
        
        self.logger.info(f"Monitoring loop ended for task {self.task_id}")
    
    def _perform_monitoring_cycle(self, task):
        """Perform a single monitoring cycle"""
        if not self.page:
            raise Exception("No browser page available")

        self.logger.debug(f"Performing monitoring cycle for task {self.task_id}")

        try:
            # 检查页面连接状态
            current_url = self.page.url
            self.logger.debug(f"Current page URL: {current_url}")

            # 刷新页面获取最新评论
            self.page.refresh()
            time.sleep(self.config.page_load_delay)

        except Exception as e:
            self.logger.warning(f"Page refresh failed, attempting to reconnect: {e}")
            # 尝试重新导航到页面
            try:
                self.page.get(task.post_url)
                time.sleep(self.config.page_load_delay)
            except Exception as reconnect_error:
                self.logger.error(f"Failed to reconnect to page: {reconnect_error}")
                raise
        
        # Get current comment count
        current_comment_count = self._get_comment_count()
        
        # Check if there are new comments
        if current_comment_count > self.last_comment_count:
            self.logger.info(
                f"Task {self.task_id}: New comments detected "
                f"({self.last_comment_count} -> {current_comment_count})"
            )
            
            # Process new comments
            new_flash_sales = self._process_new_comments(
                task.post_url, current_comment_count
            )
            
            if new_flash_sales:
                self.total_flash_sales_found += len(new_flash_sales)
                self.logger.info(
                    f"Task {self.task_id}: Found {len(new_flash_sales)} new flash sales"
                )
            
            self.last_comment_count = current_comment_count
        
        # Update task statistics
        self.task_manager.update_task_stats(
            self.task_id, current_comment_count, self.total_flash_sales_found
        )
        
        # Save task state
        self._save_task_state(task.post_url, current_comment_count)
    
    def _get_comment_count(self) -> int:
        """Get the current comment count from the page"""
        try:
            # Look for comment count indicator
            # This is specific to LowEndTalk forum structure
            comment_elements = self.page.eles('css:.Item.Comment')
            return len(comment_elements)
            
        except Exception as e:
            self.logger.warning(f"Failed to get comment count: {e}")
            return self.last_comment_count
    
    def _process_new_comments(self, post_url: str, current_comment_count: int) -> list:
        """Process new comments and detect flash sales"""
        flash_sales_found = []
        
        try:
            # Calculate which page has the newest comments
            last_page_number = max(1, (current_comment_count + 29) // 30)  # LowEndTalk: 30 comments per page
            
            # Navigate to the last page if not already there
            if last_page_number > 1:
                last_page_url = f"{post_url}/p{last_page_number}"
                self.page.get(last_page_url)
                time.sleep(self.config.page_load_delay)
            
            # Get all comments on the current page
            comment_elements = self.page.eles('css:.Item.Comment')
            
            # Process comments from newest to oldest until we find the last processed comment
            for comment_element in reversed(comment_elements):
                try:
                    # Extract comment content
                    comment_content = self._extract_comment_content(comment_element)
                    
                    # Check if this is the last processed comment
                    if comment_content == self.last_processed_comment_content:
                        break  # Stop processing, we've reached previously processed content
                    
                    # Analyze comment for flash sales
                    if self.flash_sale_detector.analyze_comment(comment_content):
                        flash_sale_info = {
                            "post_url": post_url,
                            "comment_content": comment_content,
                            "detected_at": time.time(),
                            "task_id": self.task_id
                        }
                        flash_sales_found.append(flash_sale_info)
                        
                        self.logger.info(f"Flash sale detected in task {self.task_id}")
                
                except Exception as e:
                    self.logger.warning(f"Error processing comment: {e}")
                    continue
            
            # Update last processed comment content
            if comment_elements:
                self.last_processed_comment_content = self._extract_comment_content(
                    comment_elements[-1]  # Last (newest) comment
                )
        
        except Exception as e:
            self.logger.error(f"Error processing new comments: {e}")
        
        return flash_sales_found
    
    def _extract_comment_content(self, comment_element) -> str:
        """Extract text content from a comment element"""
        try:
            # Get the comment body text
            content_element = comment_element.ele('css:.Message', timeout=2)
            if content_element:
                return content_element.text.strip()
            return ""
        except:
            return ""
    
    def _load_task_state(self, post_url: str):
        """Load task state from persistent storage"""
        try:
            state = self.state_manager.load_state()
            post_state = state.get("processed_posts", {}).get(post_url, {})
            
            self.last_comment_count = post_state.get("last_comment_count", 0)
            self.last_processed_comment_content = post_state.get("last_comment_content", "")
            
            self.logger.info(
                f"Loaded state for task {self.task_id}: "
                f"last_comment_count={self.last_comment_count}"
            )
            
        except Exception as e:
            self.logger.warning(f"Failed to load task state: {e}")
    
    def _save_task_state(self, post_url: str, comment_count: int):
        """Save task state to persistent storage"""
        try:
            state = self.state_manager.load_state()
            
            if "processed_posts" not in state:
                state["processed_posts"] = {}
            
            state["processed_posts"][post_url] = {
                "last_comment_count": comment_count,
                "last_comment_content": self.last_processed_comment_content,
                "last_updated": time.time(),
                "task_id": self.task_id
            }
            
            self.state_manager.save_state(state)
            
        except Exception as e:
            self.logger.warning(f"Failed to save task state: {e}")


class TaskExecutionEngine:
    """
    Main execution engine that manages multiple crawling tasks
    """

    def __init__(self, config: CrawlerConfig, task_manager):
        self.config = config
        self.task_manager = task_manager
        self.browser_manager = TaskBrowserManager(config)
        self.logger = logging.getLogger("task_execution_engine")
        
        # Active task executors
        self.executors: Dict[str, TaskExecutor] = {}
        self.lock = threading.Lock()
        
        self.logger.info("TaskExecutionEngine initialized")
    
    async def start_task(self, task_id: str):
        """Start executing a specific task"""
        with self.lock:
            if task_id in self.executors:
                self.logger.warning(f"Task {task_id} is already running")
                return
            
            # Create task executor
            executor = TaskExecutor(
                task_id, self.config, self.browser_manager, self.task_manager
            )
            
            self.executors[task_id] = executor
        
        # Start the executor
        await executor.start()
    
    async def stop_task(self, task_id: str):
        """Stop executing a specific task"""
        with self.lock:
            executor = self.executors.get(task_id)
            if not executor:
                self.logger.warning(f"Task {task_id} is not running")
                return
        
        # Stop the executor
        await executor.stop()
        
        with self.lock:
            del self.executors[task_id]
    
    async def stop_all_tasks(self):
        """Stop all running tasks"""
        with self.lock:
            task_ids = list(self.executors.keys())
        
        self.logger.info(f"Stopping {len(task_ids)} running tasks")
        
        # Stop all executors
        for task_id in task_ids:
            await self.stop_task(task_id)
    
    def get_running_tasks(self) -> Set[str]:
        """Get set of currently running task IDs"""
        with self.lock:
            return set(self.executors.keys())
    
    def cleanup(self):
        """Clean up all resources"""
        self.browser_manager.cleanup_all()
        self.logger.info("TaskExecutionEngine cleanup complete")
