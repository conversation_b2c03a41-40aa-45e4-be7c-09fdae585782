#!/usr/bin/env python3
"""
Test Optimized Cloudflare Bypass
"""

import time
import logging
from cloudflare_bypass_optimized import OptimizedCloudflareBypass, create_optimized_page, navigate_with_optimization

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_optimized_bypass():
    """Test the optimized Cloudflare bypass"""
    logger.info("🧪 Testing Optimized Cloudflare Bypass")
    
    try:
        # Create optimized page
        logger.info("Creating optimized browser page...")
        page = create_optimized_page()
        logger.info("✅ Optimized page created")
        
        # Test NodeSeek
        url = "https://www.nodeseek.com/"
        logger.info(f"Testing {url}...")
        
        success = navigate_with_optimization(page, url)
        
        if success:
            logger.info("✅ Navigation successful!")
            
            # Check page content
            title = page.title
            logger.info(f"Page title: {title}")
            
            # Wait a bit more to see if <PERSON>flare resolves
            logger.info("Waiting additional time for full page load...")
            time.sleep(10)
            
            # Check again
            final_title = page.title
            page_text = page.html[:1000]
            
            logger.info(f"Final title: {final_title}")
            logger.info(f"Page content preview: {page_text[:200]}...")
            
            # Check for success indicators
            if 'nodeseek' in page_text.lower() and '请稍候' not in final_title:
                logger.info("🎉 SUCCESS: NodeSeek accessed successfully!")
            elif '请稍候' in final_title:
                logger.warning("⚠️ Still showing Cloudflare challenge page")
            else:
                logger.info("ℹ️ Page loaded but content unclear")
            
            # Keep browser open for manual inspection
            logger.info("🔍 Browser will stay open for 60 seconds for manual inspection...")
            logger.info("You can manually interact with the page to test Cloudflare bypass")
            time.sleep(60)
            
        else:
            logger.error("❌ Navigation failed")
        
        page.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


def test_task_manager_integration():
    """Test with task manager integration"""
    logger.info("🧪 Testing Task Manager Integration")
    
    try:
        from task_browser_manager import TaskBrowserManager
        from improved_forum_crawler import CrawlerConfig
        
        # Create config with optimized settings
        config = CrawlerConfig()
        config.headless = False  # Important for Cloudflare
        config.page_load_delay = 5.0  # Longer delay
        
        # Create browser manager
        browser_manager = TaskBrowserManager(config)
        
        # Test creating a task tab
        task_id = "test_optimized"
        post_url = "https://www.nodeseek.com/"
        domain = "nodeseek.com"
        
        logger.info("Creating task tab with optimized settings...")
        page = browser_manager.create_task_tab(task_id, post_url, domain)
        
        if page:
            logger.info("✅ Task tab created successfully")
            
            # Check page
            title = page.title
            logger.info(f"Page title: {title}")
            
            # Wait for potential Cloudflare resolution
            logger.info("Waiting for page to fully load...")
            time.sleep(15)
            
            # Final check
            final_title = page.title
            logger.info(f"Final title: {final_title}")
            
            if '请稍候' not in final_title:
                logger.info("🎉 SUCCESS: Cloudflare challenge appears to be resolved!")
            else:
                logger.warning("⚠️ Cloudflare challenge still present")
            
            # Keep open for inspection
            logger.info("Browser open for 30 seconds for inspection...")
            time.sleep(30)
            
            # Close tab
            browser_manager.close_task_tab(task_id)
            
        else:
            logger.error("❌ Failed to create task tab")
        
        # Cleanup
        browser_manager.cleanup_all()
        
    except Exception as e:
        logger.error(f"❌ Task manager test failed: {e}")


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Optimized Cloudflare Bypass')
    parser.add_argument('--method', choices=['direct', 'task'], default='direct',
                       help='Test method')
    
    args = parser.parse_args()
    
    print("🛡️ Optimized Cloudflare Bypass Test")
    print("=" * 40)
    print("This test uses realistic browser settings to bypass Cloudflare")
    print("The browser will open in visible mode for manual verification")
    print("=" * 40)
    
    if args.method == 'direct':
        test_optimized_bypass()
    elif args.method == 'task':
        test_task_manager_integration()


if __name__ == "__main__":
    main()
