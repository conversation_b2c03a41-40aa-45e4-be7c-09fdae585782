#!/usr/bin/env python3
"""
测试可见浏览器和 Cloudflare 处理
"""

import requests
import time
import json

API_BASE = "http://127.0.0.1:8001"

def test_visible_browser_with_nodeseek():
    """测试可见浏览器访问 NodeSeek"""
    print("🧪 测试可见浏览器访问 NodeSeek")
    print("=" * 50)
    
    # 创建 NodeSeek 任务
    task_data = {
        "post_url": "https://www.nodeseek.com/",
        "forum_domain": "nodeseek.com",
        "monitor_interval": 60,
        "ai_analysis_enabled": True
    }
    
    print("1. 创建 NodeSeek 任务...")
    response = requests.post(f"{API_BASE}/tasks", json=task_data)
    
    if response.status_code != 200:
        print(f"❌ 任务创建失败: {response.text}")
        return
    
    task = response.json()
    task_id = task["id"]
    print(f"✅ 任务创建成功: {task_id}")
    print(f"   任务状态: {task['status']}")
    
    # 启动任务
    print(f"\n2. 启动任务 {task_id[:8]}...")
    response = requests.put(f"{API_BASE}/tasks/{task_id}/start")
    
    if response.status_code != 200:
        print(f"❌ 任务启动失败: {response.text}")
        return
    
    print("✅ 任务启动成功")
    print("🔍 现在应该有一个可见的 Chrome 浏览器窗口打开")
    print("📋 浏览器配置:")
    print("   - 有头模式 (可见)")
    print("   - User-Agent: Chrome *********")
    print("   - 窗口大小: 1366x768")
    print("   - 启用 JavaScript 和图片")
    print("   - 专用用户数据目录")
    
    # 等待一段时间让用户观察
    print(f"\n3. 等待 30 秒让你观察浏览器窗口...")
    print("   请检查:")
    print("   - 是否有 Chrome 窗口打开")
    print("   - 窗口是否显示 NodeSeek 网站")
    print("   - 是否遇到 Cloudflare 挑战")
    print("   - 如果有挑战，是否可以手动点击验证")
    
    for i in range(30, 0, -5):
        print(f"   剩余时间: {i} 秒...")
        time.sleep(5)
    
    # 检查任务状态
    print(f"\n4. 检查任务状态...")
    response = requests.get(f"{API_BASE}/tasks/{task_id}")
    
    if response.status_code == 200:
        task_status = response.json()
        print(f"   任务状态: {task_status['status']}")
        print(f"   开始时间: {task_status.get('started_at', 'N/A')}")
        print(f"   最后检查: {task_status.get('last_check_at', 'N/A')}")
        print(f"   评论数量: {task_status.get('comment_count', 0)}")
        print(f"   发现的闪购: {task_status.get('flash_sales_found', 0)}")
        
        if task_status.get('error_message'):
            print(f"   错误信息: {task_status['error_message']}")
    
    # 停止任务
    print(f"\n5. 停止任务...")
    response = requests.put(f"{API_BASE}/tasks/{task_id}/stop")
    
    if response.status_code == 200:
        print("✅ 任务停止成功")
    else:
        print(f"⚠️ 任务停止失败: {response.text}")
    
    # 清理任务
    print(f"\n6. 清理任务...")
    response = requests.delete(f"{API_BASE}/tasks/{task_id}")
    
    if response.status_code == 200:
        print("✅ 任务清理成功")
    else:
        print(f"⚠️ 任务清理失败: {response.text}")
    
    print(f"\n🎉 测试完成！")


def test_long_running_task():
    """测试长时间运行的任务（用于手动 Cloudflare 测试）"""
    print("🧪 测试长时间运行任务（手动 Cloudflare 测试）")
    print("=" * 60)
    
    # 创建任务
    task_data = {
        "post_url": "https://www.nodeseek.com/",
        "forum_domain": "nodeseek.com", 
        "monitor_interval": 120,  # 2分钟间隔
        "ai_analysis_enabled": True
    }
    
    print("1. 创建长时间运行任务...")
    response = requests.post(f"{API_BASE}/tasks", json=task_data)
    
    if response.status_code != 200:
        print(f"❌ 任务创建失败: {response.text}")
        return
    
    task = response.json()
    task_id = task["id"]
    print(f"✅ 任务创建成功: {task_id}")
    
    # 启动任务
    print(f"\n2. 启动任务...")
    response = requests.put(f"{API_BASE}/tasks/{task_id}/start")
    
    if response.status_code != 200:
        print(f"❌ 任务启动失败: {response.text}")
        return
    
    print("✅ 任务启动成功")
    print("\n🔍 浏览器窗口应该已经打开")
    print("📋 请手动测试以下内容:")
    print("   1. 观察浏览器窗口是否可见")
    print("   2. 检查是否显示 Cloudflare 挑战")
    print("   3. 尝试手动完成 Cloudflare 验证")
    print("   4. 观察页面是否成功加载 NodeSeek")
    print("   5. 检查任务是否正常运行")
    
    print(f"\n⏰ 任务将运行 5 分钟，每 2 分钟检查一次状态")
    print("   按 Ctrl+C 可以提前结束测试")
    
    try:
        # 运行 5 分钟，每 30 秒检查一次状态
        for i in range(10):
            time.sleep(30)
            
            # 检查任务状态
            response = requests.get(f"{API_BASE}/tasks/{task_id}")
            if response.status_code == 200:
                task_status = response.json()
                print(f"\n📊 状态检查 {i+1}/10:")
                print(f"   任务状态: {task_status['status']}")
                print(f"   评论数量: {task_status.get('comment_count', 0)}")
                print(f"   最后检查: {task_status.get('last_check_at', 'N/A')}")
                
                if task_status.get('error_message'):
                    print(f"   ❌ 错误: {task_status['error_message']}")
                    break
            else:
                print(f"⚠️ 无法获取任务状态: {response.status_code}")
    
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断测试")
    
    # 停止并清理任务
    print(f"\n🛑 停止并清理任务...")
    requests.put(f"{API_BASE}/tasks/{task_id}/stop")
    requests.delete(f"{API_BASE}/tasks/{task_id}")
    print("✅ 清理完成")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试可见浏览器')
    parser.add_argument('--mode', choices=['quick', 'long'], default='quick',
                       help='测试模式: quick=快速测试, long=长时间测试')
    
    args = parser.parse_args()
    
    print("🌐 可见浏览器测试")
    print("=" * 30)
    print("此测试将:")
    print("- 创建一个访问 NodeSeek 的任务")
    print("- 启动可见的 Chrome 浏览器")
    print("- 测试 Cloudflare 绕过功能")
    print("- 允许手动验证和观察")
    print("=" * 30)
    
    if args.mode == 'quick':
        test_visible_browser_with_nodeseek()
    elif args.mode == 'long':
        test_long_running_task()


if __name__ == "__main__":
    main()
