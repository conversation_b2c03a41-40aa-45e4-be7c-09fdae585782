#!/usr/bin/env python3
"""
Test script to verify worker thread functionality
"""

import sys
import os
import time
import threading
import queue
import logging

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import ForumCrawler, CrawlerConfig, Crawler<PERSON>ogger


def test_worker_thread_basic_functionality():
    """Test basic worker thread functionality with mock tasks"""
    print("Testing worker thread basic functionality...")
    
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "test_worker.log")
    
    # Create minimal config
    config = CrawlerConfig()
    config.num_workers = 1  # Use only 1 worker for testing
    config.queue_timeout = 10  # Shorter timeout for testing
    config.monitor_timeout = 30
    
    # Create crawler instance
    crawler = ForumCrawler(config)
    
    # Create a mock task
    mock_task = {
        "post_url": "https://lowendtalk.com/discussion/test",
        "post_title": "Test Post for Worker Thread",
        "current_comment_count": 5,
        "datetime_attr": "2025-07-14T15:08:59+00:00"
    }
    
    # Add task to queue
    crawler.task_queue.put(mock_task)
    print(f"Added mock task to queue. Queue size: {crawler.task_queue.qsize()}")
    
    # Set monitor ready event (simulate monitor being ready)
    crawler.monitor_ready_event.set()
    print("Set monitor ready event")
    
    # Create a simple browser manager mock
    class MockBrowserManager:
        def create_tab(self):
            print("Mock: Creating browser tab")
            return MockPage()
    
    class MockPage:
        def get(self, url):
            print(f"Mock: Navigating to {url}")
            time.sleep(1)  # Simulate page load
        
        def wait(self):
            return self
        
        def load_start(self):
            pass
        
        def eles(self, selector):
            print(f"Mock: Finding elements with selector {selector}")
            return []  # Return empty list for testing
        
        def close(self):
            print("Mock: Closing page")
    
    # Test worker thread function directly
    mock_browser_manager = MockBrowserManager()
    
    print("Starting worker thread test...")
    
    # Create and start worker thread
    worker_thread = threading.Thread(
        target=crawler._worker_thread_func,
        args=("TestWorker", mock_browser_manager),
        name="TestWorker",
        daemon=True
    )
    
    worker_thread.start()
    print("Worker thread started")
    
    # Wait for worker to process the task
    start_time = time.time()
    timeout = 30  # 30 second timeout
    
    while crawler.task_queue.qsize() > 0 and time.time() - start_time < timeout:
        print(f"Waiting for task processing... Queue size: {crawler.task_queue.qsize()}")
        time.sleep(2)
    
    if crawler.task_queue.qsize() == 0:
        print("✅ SUCCESS: Worker thread processed the task!")
    else:
        print("❌ FAILURE: Worker thread did not process the task within timeout")
    
    # Wait a bit more to see worker thread logs
    time.sleep(5)
    
    print("Test completed")


def test_queue_operations():
    """Test basic queue operations"""
    print("\nTesting queue operations...")
    
    test_queue = queue.Queue()
    
    # Test putting items
    test_items = [
        {"id": 1, "title": "Test 1"},
        {"id": 2, "title": "Test 2"},
        {"id": 3, "title": "Test 3"}
    ]
    
    for item in test_items:
        test_queue.put(item)
        print(f"Added item {item['id']} to queue. Size: {test_queue.qsize()}")
    
    # Test getting items
    while not test_queue.empty():
        try:
            item = test_queue.get(timeout=1)
            print(f"Got item {item['id']} from queue. Remaining: {test_queue.qsize()}")
            test_queue.task_done()
        except queue.Empty:
            print("Queue is empty")
            break
    
    print("✅ Queue operations test completed")


def test_monitor_ready_event():
    """Test monitor ready event functionality"""
    print("\nTesting monitor ready event...")
    
    ready_event = threading.Event()
    
    def worker_simulation():
        print("Worker: Waiting for monitor ready event...")
        if ready_event.wait(timeout=10):
            print("✅ Worker: Monitor ready event received!")
        else:
            print("❌ Worker: Timeout waiting for monitor ready event")
    
    def monitor_simulation():
        print("Monitor: Starting...")
        time.sleep(2)  # Simulate monitor initialization
        print("Monitor: Setting ready event")
        ready_event.set()
    
    # Start threads
    worker_thread = threading.Thread(target=worker_simulation, daemon=True)
    monitor_thread = threading.Thread(target=monitor_simulation, daemon=True)
    
    worker_thread.start()
    monitor_thread.start()
    
    # Wait for completion
    worker_thread.join(timeout=15)
    monitor_thread.join(timeout=15)
    
    print("Monitor ready event test completed")


if __name__ == "__main__":
    print("Worker Thread Functionality Test")
    print("=" * 50)
    
    try:
        test_queue_operations()
        test_monitor_ready_event()
        test_worker_thread_basic_functionality()
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("All tests completed!")
