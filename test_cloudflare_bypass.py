#!/usr/bin/env python3
"""
Test Cloudflare Bypass Functionality
Tests the enhanced anti-detection capabilities
"""

import time
import logging
from cloudflare_bypass import CloudflareBypass, create_cloudflare_resistant_page, navigate_with_cloudflare_bypass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_cloudflare_bypass():
    """Test Cloudflare bypass with various protected sites"""
    
    # Test URLs (some may have Cloudflare protection)
    test_urls = [
        "https://www.nodeseek.com/",
        "https://lowendtalk.com/",
        "https://httpbin.org/user-agent",  # To check user agent
        "https://bot.sannysoft.com/",      # Bot detection test
    ]
    
    logger.info("🧪 Testing Cloudflare Bypass Functionality")
    
    try:
        # Create stealth page
        logger.info("Creating stealth browser page...")
        page = create_cloudflare_resistant_page()
        logger.info("✅ Stealth page created successfully")
        
        for i, url in enumerate(test_urls, 1):
            logger.info(f"\n📋 Test {i}: Testing {url}")
            
            try:
                # Navigate with bypass
                success = navigate_with_cloudflare_bypass(page, url)
                
                if success:
                    logger.info(f"✅ Successfully accessed {url}")
                    
                    # Get page title and check for success indicators
                    title = page.title
                    logger.info(f"   Page title: {title}")
                    
                    # Check if we're blocked
                    page_text = page.html.lower()
                    blocked_indicators = [
                        'access denied',
                        'blocked',
                        'forbidden',
                        'checking your browser',
                        'ddos protection'
                    ]
                    
                    is_blocked = any(indicator in page_text for indicator in blocked_indicators)
                    
                    if is_blocked:
                        logger.warning(f"⚠️  Page may be blocked or showing challenge")
                    else:
                        logger.info(f"✅ Page loaded successfully without blocks")
                    
                    # For user-agent test, show the detected user agent
                    if "httpbin.org/user-agent" in url:
                        try:
                            user_agent_info = page.ele('css:pre').text
                            logger.info(f"   Detected User-Agent: {user_agent_info}")
                        except:
                            pass
                    
                    # For bot detection test, check results
                    if "bot.sannysoft.com" in url:
                        try:
                            # Wait for page to load completely
                            time.sleep(5)
                            
                            # Check for detection results
                            results = page.eles('css:.result')
                            if results:
                                logger.info("   Bot detection results:")
                                for result in results[:5]:  # Show first 5 results
                                    try:
                                        text = result.text.strip()
                                        if text:
                                            logger.info(f"     {text}")
                                    except:
                                        pass
                        except Exception as e:
                            logger.debug(f"Error checking bot detection: {e}")
                    
                else:
                    logger.error(f"❌ Failed to access {url}")
                
                # Wait between tests
                time.sleep(3)
                
            except Exception as e:
                logger.error(f"❌ Error testing {url}: {e}")
        
        # Close the page
        page.close()
        logger.info("\n✅ All tests completed")
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


def test_specific_site(url: str):
    """Test a specific site with detailed logging"""
    logger.info(f"🎯 Testing specific site: {url}")
    
    try:
        bypass = CloudflareBypass()
        page = bypass.create_stealth_page()
        
        logger.info("Attempting navigation with detailed monitoring...")
        
        # Navigate with detailed monitoring
        success = bypass.navigate_with_retry(page, url, max_retries=3)
        
        if success:
            logger.info("✅ Navigation successful!")
            
            # Detailed page analysis
            title = page.title
            logger.info(f"Page title: {title}")
            
            # Check page content
            try:
                # Look for common elements
                body_text = page.ele('css:body').text[:500] if page.ele('css:body') else "No body found"
                logger.info(f"Page content preview: {body_text}...")
                
                # Check for Cloudflare indicators
                cf_indicators = [
                    'cloudflare', 'cf-ray', 'checking your browser',
                    'ddos protection', 'just a moment'
                ]
                
                page_html = page.html.lower()
                found_indicators = [ind for ind in cf_indicators if ind in page_html]
                
                if found_indicators:
                    logger.warning(f"⚠️  Cloudflare indicators found: {found_indicators}")
                else:
                    logger.info("✅ No Cloudflare protection detected")
                
            except Exception as e:
                logger.warning(f"Error analyzing page content: {e}")
        
        else:
            logger.error("❌ Navigation failed after all retries")
        
        page.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Cloudflare Bypass')
    parser.add_argument('--url', help='Test specific URL')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose logging')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    print("🛡️  Cloudflare Bypass Test Suite")
    print("=" * 50)
    
    if args.url:
        test_specific_site(args.url)
    else:
        test_cloudflare_bypass()


if __name__ == "__main__":
    main()
