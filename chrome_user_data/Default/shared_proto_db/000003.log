�f�5            �f�5            �f�5            �
���           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (����10�ć1X          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (����10���           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (����10�6x�           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (����10�:7}`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (����10KɁނ          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (����10�~�6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (����10"xa          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (����10�'�W	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (����10{�{� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (����10;Jv�1           	39_config
��؈��O�ԓ ����1��]i~           	39_configf
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1j�;� 
          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ��������           	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1g\P�;          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ ����1
���Åօ�C�ԓ ����1
�����Ӆ���ԓ ����1
��������_�ԓ ����1=��o�          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ ����1
���Åօ�C�ԓ ����1
�����Ӆ���ԓ ����1
��������_�ԓ ����1
�����������I ����1
�������I ����1
ʒ���қ�C��I ����1
���޾���,��I ����1
���������I ����1�jI�P          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ ����1
���Åօ�C�ԓ ����1
�����Ӆ���ԓ ����1
��������_�ԓ ����1
�����������I ����1
�������I ����1
ʒ���қ�C��I ����1
���޾���,��I ����1
���������I ����1
������t�ԓ ����1
��������k�ԓ ����1
գ��������ԓ ����1
��ר�ٳ���ԓ ����1
ෛ�������ԓ ����1
������Ʉ��ԓ ����1��К�          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ ����1
���Åօ�C�ԓ ����1
�����Ӆ���ԓ ����1
��������_�ԓ ����1
�����������I ����1
�������I ����1
ʒ���қ�C��I ����1
���޾���,��I ����1
���������I ����1
������t�ԓ ����1
��������k�ԓ ����1
գ��������ԓ ����1
��ר�ٳ���ԓ ����1
ෛ�������ԓ ����1
������Ʉ��ԓ ����1
"��ї�Z�ԓ ����1(Ȏ�������
#�򖐩�����ԓ ����1(Ȏ�������
#�ɕԺ����ԓ ����1(Ȏ�����������           20_1_1
1��'�            ����       .   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_BatterySaverMode
IPH_BatterySaverMode��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo��4_IPH_GMCCastStartStop
IPH_GMCCastStartStop��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��4_IPH_LiveCaption
IPH_LiveCaption��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_ProfileSwitch
IPH_ProfileSwitch��4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��'4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop���)���B          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    �훿ݐ�$


   ?ShoppingUserOther  (����10``��#C          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONK��iFD          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSg��E          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2
�5�DF          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��dN'G          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��%H          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING]9��>I          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION֓~%#J          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION���#K          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONkT&R#L          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONOv��FM          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS)Tv�FN          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�ʅFO          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�b�P          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2qiw�DQ          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�_�;'R          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��?#%S          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING)��E>T          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONB����U          021_download,ac095649-d1f7-4a55-b25f-0408cc8a478d�
�
$ac095649-d1f7-4a55-b25f-0408cc8a478d
������ɦ�"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ��э������������� �� � � � � � � ����������������������


     �v��HV          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-GIcQwB7enh_CRDSkX8dFAUtBd85Cr9RfqGicYrYRw-TxNOKiqyhDUlQbooyJmLqonYAYNNVA vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:13 GMT expires:Wed, 16 Jul 2025 03:42:13 GMT accept-ranges:bytes x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION����W          021_download,ac095649-d1f7-4a55-b25f-0408cc8a478d�
�
$ac095649-d1f7-4a55-b25f-0408cc8a478d
������ɦ�"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  4 8 3 4 5 9 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d x ��э������������� �� �� � � � � ����������������������


     캫}HX          021_download,5b01014c-fe82-4e62-99e9-594140d943c5�
�
$5b01014c-fe82-4e62-99e9-594140d943c5
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj       r       x ��֍������������� �� � � � � � � ����������������������


     021_download,ac095649-d1f7-4a55-b25f-0408cc8a478d�
�
$ac095649-d1f7-4a55-b25f-0408cc8a478d
������ɦ�"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  4 8 3 4 5 9 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d x����э������������� �� �� � � � � ����������������������


     �¢�|Z          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-esxDl6Fx5HNOAiEWBc4_Qg0DRxNdDQTvqpD53_u64E0W_xEeA6bF-ZFn18fLiNmjEPmEjJfw cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:13 GMT expires:Wed, 16 Jul 2025 03:42:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS72#|�[          021_download,5b01014c-fe82-4e62-99e9-594140d943c5�
�
$5b01014c-fe82-4e62-99e9-594140d943c5
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 2 2 3 2 8 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 0 1 0 1 4 c - f e 8 2 - 4 e 6 2 - 9 9 e 9 - 5 9 4 1 4 0 d 9 4 3 c 5 x ��֍������������� �� �� � � � � ����������������������


     �����\          021_download,5b01014c-fe82-4e62-99e9-594140d943c5�
�
$5b01014c-fe82-4e62-99e9-594140d943c5
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 0 1 0 1 4 c - f e 8 2 - 4 e 6 2 - 9 9 e 9 - 5 9 4 1 4 0 d 9 4 3 c 5 r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 0 1 0 1 4 c - f e 8 2 - 4 e 6 2 - 9 9 e 9 - 5 9 4 1 4 0 d 9 4 3 c 5 xڎ��֍����֍��� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     �ƌ]          &9_5b01014c-fe82-4e62-99e9-594140d943c5�	$5b01014c-fe82-4e62-99e9-594140d943c5��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\5b01014c-fe82-4e62-99e9-594140d943c58����ݐ�@ɚ��ݐ�HPϟ�/Xڎ`ɚ��ݐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-esxDl6Fx5HNOAiEWBc4_Qg0DRxNdDQTvqpD53_u64E0W_xEeA6bF-ZFn18fLiNmjEPmEjJfw cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:13 GMT expires:Wed, 16 Jul 2025 03:42:13 GMT accept-ranges:bytes vary:X-Goog-Api-Key x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS2NK�^          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2���_          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2-}}��`          021_download,5b01014c-fe82-4e62-99e9-594140d943c5�
�
$5b01014c-fe82-4e62-99e9-594140d943c5
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 0 1 0 1 4 c - f e 8 2 - 4 e 6 2 - 9 9 e 9 - 5 9 4 1 4 0 d 9 4 3 c 5 r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 5 b 0 1 0 1 4 c - f e 8 2 - 4 e 6 2 - 9 9 e 9 - 5 9 4 1 4 0 d 9 4 3 c 5 xڎ��֍����֍��� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     �mt�> a           021_download,5b01014c-fe82-4e62-99e9-594140d943c5�g�l�b          021_download,ac095649-d1f7-4a55-b25f-0408cc8a478d�
�
$ac095649-d1f7-4a55-b25f-0408cc8a478d
������ɦ�"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d x���э����׍��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     ��>�Xc          &9_ac095649-d1f7-4a55-b25f-0408cc8a478d�	$ac095649-d1f7-4a55-b25f-0408cc8a478d��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\ac095649-d1f7-4a55-b25f-0408cc8a478d8����ݐ�@����ݐ�HPϟ�/X�`����ݐ�p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-GIcQwB7enh_CRDSkX8dFAUtBd85Cr9RfqGicYrYRw-TxNOKiqyhDUlQbooyJmLqonYAYNNVA vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:13 GMT expires:Wed, 16 Jul 2025 03:42:13 GMT accept-ranges:bytes x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��I�V d          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600�S�r�427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSȥ)�De          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSk=wW�f          021_download,ac095649-d1f7-4a55-b25f-0408cc8a478d�
�
$ac095649-d1f7-4a55-b25f-0408cc8a478d
������ɦ�"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a c 0 9 5 6 4 9 - d 1 f 7 - 4 a 5 5 - b 2 5 f - 0 4 0 8 c c 8 a 4 7 8 d x���э����׍��� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     Y�> g           021_download,ac095649-d1f7-4a55-b25f-0408cc8a478dꜺ&�h          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��؍������������� �� � � � � � � ����������������������


     t|$�i          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-syRpL7kcaVUebzrfwrsoaz5DhFvtQ_M4jSfi38-KKSrF8JGszVqF3wkZADQQLDV5DmWBIwPM accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:14 GMT expires:Wed, 16 Jul 2025 03:42:14 GMT x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�O�.pj          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  3 6 5 2 0 6 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d x ��؍������������� �� �� � � � � ����������������������


     f�o�8k          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  3 6 5 2 0 6 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d x��8��؍������������� �� �� � � � � ����������������������


     021_download,d878cd53-f0c2-4303-8bf8-8600427cdc87�
�
$d878cd53-f0c2-4303-8bf8-8600427cdc87
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��ߍ������������� �� � � � � � � ����������������������


     ldym          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-hwbR_P6q-13FBMoYJuzSlHoC_5wgrW53CWvg_aV-q4yQfW46mhn5ZixcQnbI-Tqn_PLOpwME date:Tue, 15 Jul 2025 03:42:14 GMT expires:Wed, 16 Jul 2025 03:42:14 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�3�F<n          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  3 6 5 2 0 6 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d x�����؍������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     021_download,d878cd53-f0c2-4303-8bf8-8600427cdc87�
�
$d878cd53-f0c2-4303-8bf8-8600427cdc87
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 6 6 9 8 3 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 8 7 8 c d 5 3 - f 0 c 2 - 4 3 0 3 - 8 b f 8 - 8 6 0 0 4 2 7 c d c 8 7 x����ߍ������������� \�S�]���s�b0��l?�D}�F��.�"&�r�� �� � � � � ����������������������


     /�Gدp          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d x�����؍��������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     ����%q          &9_9c79dbc2-a079-441d-9cad-46d309d3c26d�	$9c79dbc2-a079-441d-9cad-46d309d3c26d��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\9c79dbc2-a079-441d-9cad-46d309d3c26d8����ݐ�@����ݐ�HPϟ�/X���`����ݐ�p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-syRpL7kcaVUebzrfwrsoaz5DhFvtQ_M4jSfi38-KKSrF8JGszVqF3wkZADQQLDV5DmWBIwPM accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:14 GMT expires:Wed, 16 Jul 2025 03:42:14 GMT x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2��'r          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING���'s          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING:��u�t          021_download,9c79dbc2-a079-441d-9cad-46d309d3c26d�
�
$9c79dbc2-a079-441d-9cad-46d309d3c26d
������ɦ�"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 9 c 7 9 d b c 2 - a 0 7 9 - 4 4 1 d - 9 c a d - 4 6 d 3 0 9 d 3 c 2 6 d x�����؍��������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     5Z
> u           021_download,9c79dbc2-a079-441d-9cad-46d309d3c26dyG[��v          021_download,d878cd53-f0c2-4303-8bf8-8600427cdc87�
�
$d878cd53-f0c2-4303-8bf8-8600427cdc87
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 8 7 8 c d 5 3 - f 0 c 2 - 4 3 0 3 - 8 b f 8 - 8 6 0 0 4 2 7 c d c 8 7 r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 8 7 8 c d 5 3 - f 0 c 2 - 4 3 0 3 - 8 b f 8 - 8 6 0 0 4 2 7 c d c 8 7 x����ߍ��������� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     ��䗉w          &9_d878cd53-f0c2-4303-8bf8-8600427cdc87�	$d878cd53-f0c2-4303-8bf8-8600427cdc87��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\d878cd53-f0c2-4303-8bf8-8600427cdc878΢��ݐ�@؍��ݐ�HPϟ�/X��`؍��ݐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-hwbR_P6q-13FBMoYJuzSlHoC_5wgrW53CWvg_aV-q4yQfW46mhn5ZixcQnbI-Tqn_PLOpwME date:Tue, 15 Jul 2025 03:42:14 GMT expires:Wed, 16 Jul 2025 03:42:14 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�U��%x          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING;��1%y          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@ HPϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGC��z          021_download,d878cd53-f0c2-4303-8bf8-8600427cdc87�
�
$d878cd53-f0c2-4303-8bf8-8600427cdc87
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 8 7 8 c d 5 3 - f 0 c 2 - 4 3 0 3 - 8 b f 8 - 8 6 0 0 4 2 7 c d c 8 7 r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d 8 7 8 c d 5 3 - f 0 c 2 - 4 3 0 3 - 8 b f 8 - 8 6 0 0 4 2 7 c d c 8 7 x����ߍ��������� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     ��r> {           021_download,d878cd53-f0c2-4303-8bf8-8600427cdc87�M
��|          021_download,be9cd8bc-1988-4ca0-817d-efd21baa2f2c�
�
$be9cd8bc-1988-4ca0-817d-efd21baa2f2c
������ɦ�"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     � P�8}          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_JQtEz-56qi0vLrYu536p6ndkIsuBDwuOc-6mhWa3Ht-xUXFECqQh_e1mp-DOWFdHqtM1B4Ms vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT accept-ranges:bytes x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING[L�~          021_download,be9cd8bc-1988-4ca0-817d-efd21baa2f2c�
�
$be9cd8bc-1988-4ca0-817d-efd21baa2f2c
������ɦ�"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  2 9 0 7 1 7 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b e 9 c d 8 b c - 1 9 8 8 - 4 c a 0 - 8 1 7 d - e f d 2 1 b a a 2 f 2 c x ���������������� �� �� � � � � ����������������������


     ?��          021_download,a6b2eca8-5052-4db5-90c2-a7a6100a40ed�
�
$a6b2eca8-5052-4db5-90c2-a7a6100a40ed
������ɦ�"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     qZ}zA�          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@ HPϟ�/X ` p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH89jGxk0S4x9pzUq2245XIvz4-_2kOigwOcIy6r8VqYmjUwQ7DkSEIF6Ybcha7QSrHo vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT accept-ranges:bytes x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��=J�          021_download,a6b2eca8-5052-4db5-90c2-a7a6100a40ed�
�
$a6b2eca8-5052-4db5-90c2-a7a6100a40ed
������ɦ�"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  9 7 5 3 5 3 . c r d o w n l o a d   r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 6 b 2 e c a 8 - 5 0 5 2 - 4 d b 5 - 9 0 c 2 - a 7 a 6 1 0 0 a 4 0 e d x ���������������� �� �� � � � � ����������������������


     NO,���          021_download,a6b2eca8-5052-4db5-90c2-a7a6100a40ed�
�
$a6b2eca8-5052-4db5-90c2-a7a6100a40ed
������ɦ�"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 6 b 2 e c a 8 - 5 0 5 2 - 4 d b 5 - 9 0 c 2 - a 7 a 6 1 0 0 a 4 0 e d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 6 b 2 e c a 8 - 5 0 5 2 - 4 d b 5 - 9 0 c 2 - a 7 a 6 1 0 0 a 4 0 e d x�&����������� ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     ���/P�          &9_a6b2eca8-5052-4db5-90c2-a7a6100a40ed�	$a6b2eca8-5052-4db5-90c2-a7a6100a40ed��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\a6b2eca8-5052-4db5-90c2-a7a6100a40ed8墉�ݐ�@����ݐ�HPϟ�/X�&`����ݐ�p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH89jGxk0S4x9pzUq2245XIvz4-_2kOigwOcIy6r8VqYmjUwQ7DkSEIF6Ybcha7QSrHo vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT accept-ranges:bytes x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�}�>�          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION31�>�          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONd��8��          021_download,a6b2eca8-5052-4db5-90c2-a7a6100a40ed�
�
$a6b2eca8-5052-4db5-90c2-a7a6100a40ed
������ɦ�"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 6 b 2 e c a 8 - 5 0 5 2 - 4 d b 5 - 9 0 c 2 - a 7 a 6 1 0 0 a 4 0 e d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 6 b 2 e c a 8 - 5 0 5 2 - 4 d b 5 - 9 0 c 2 - a 7 a 6 1 0 0 a 4 0 e d x�&����������� ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     -;Ȕ> �           021_download,a6b2eca8-5052-4db5-90c2-a7a6100a40ed��=��          021_download,be9cd8bc-1988-4ca0-817d-efd21baa2f2c�
�
$be9cd8bc-1988-4ca0-817d-efd21baa2f2c
������ɦ�"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b e 9 c d 8 b c - 1 9 8 8 - 4 c a 0 - 8 1 7 d - e f d 2 1 b a a 2 f 2 c r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b e 9 c d 8 b c - 1 9 8 8 - 4 c a 0 - 8 1 7 d - e f d 2 1 b a a 2 f 2 c x�֧����������� 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     Ж�I�          &9_be9cd8bc-1988-4ca0-817d-efd21baa2f2c�	$be9cd8bc-1988-4ca0-817d-efd21baa2f2c��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\be9cd8bc-1988-4ca0-817d-efd21baa2f2c8ۢ��ݐ�@����ݐ�HPϟ�/X�֧`����ݐ�p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_JQtEz-56qi0vLrYu536p6ndkIsuBDwuOc-6mhWa3Ht-xUXFECqQh_e1mp-DOWFdHqtM1B4Ms vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT accept-ranges:bytes x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGX�S?��          021_download,be9cd8bc-1988-4ca0-817d-efd21baa2f2c�
�
$be9cd8bc-1988-4ca0-817d-efd21baa2f2c
������ɦ�"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b e 9 c d 8 b c - 1 9 8 8 - 4 c a 0 - 8 1 7 d - e f d 2 1 b a a 2 f 2 c r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b e 9 c d 8 b c - 1 9 8 8 - 4 c a 0 - 8 1 7 d - e f d 2 1 b a a 2 f 2 c x�֧����������� 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     qo.> �           021_download,be9cd8bc-1988-4ca0-817d-efd21baa2f2cx�TI��          021_download,7f0669a4-d3c3-4aca-a3b2-9e40428f395c�
�
$7f0669a4-d3c3-4aca-a3b2-9e40428f395c
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     �[Z�j�          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH89OrcS8CY44DGy_1n_ULx0oXlESp7CxgNza3DutBDA22jox42fyDPEdjXQuaBTwcMU accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION՜K��          021_download,7f0669a4-d3c3-4aca-a3b2-9e40428f395c�
�
$7f0669a4-d3c3-4aca-a3b2-9e40428f395c
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   f   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 8 4 1 5 . c r d o w n l o a d r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 f 0 6 6 9 a 4 - d 3 c 3 - 4 a c a - a 3 b 2 - 9 e 4 0 4 2 8 f 3 9 5 c x ���������������� �� �� � � � � ����������������������


     �["���          021_download,7f0669a4-d3c3-4aca-a3b2-9e40428f395c�
�
$7f0669a4-d3c3-4aca-a3b2-9e40428f395c
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 f 0 6 6 9 a 4 - d 3 c 3 - 4 a c a - a 3 b 2 - 9 e 4 0 4 2 8 f 3 9 5 c r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r�o��. o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 f 0 6 6 9 a 4 - d 3 c 3 - 4 a c a - a 3 b 2 - 9 e 4 0 4 2 8 f 3 9 5 c x������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     ��@�z�          &9_7f0669a4-d3c3-4aca-a3b2-9e40428f395c�	$7f0669a4-d3c3-4aca-a3b2-9e40428f395c��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2vC:\Users\<USER>\PyCharmMiscProject\chrome_user_data\Default\Download Service\Files\7f0669a4-d3c3-4aca-a3b2-9e40428f395c8��ݐ�@����ݐ�HPϟ�/X��`����ݐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH89OrcS8CY44DGy_1n_ULx0oXlESp7CxgNza3DutBDA22jox42fyDPEdjXQuaBTwcMU accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:42:15 GMT expires:Wed, 16 Jul 2025 03:42:15 GMT x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION���          021_download,7f0669a4-d3c3-4aca-a3b2-9e40428f395c�
�
$7f0669a4-d3c3-4aca-a3b2-9e40428f395c
������ɦ�"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 f 0 6 6 9 a 4 - d 3 c 3 - 4 a c a - a 3 b 2 - 9 e 4 0 4 2 8 f 3 9 5 c r��   v   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ u s e r _ d a t a \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 7 f 0 6 6 9 a 4 - d 3 c 3 - 4 a c a - a 3 b 2 - 9 e 4 0 4 2 8 f 3 9 5 c x������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     ��> �           021_download,7f0669a4-d3c3-4aca-a3b2-9e40428f395c�7�F�          	39_config�
��؈��O�ԓ ����1
����Ą���ԓ ����1
�����ٝ���ԓ ����1
�����ؿ���ԓ ����1
�ހ���`�ԓ ����1
"�������d�ԓ ����1(���ʖ����
ۯ��Њ���ԓ ����1
��՛�����ԓ ����1
���Åօ�C�ԓ ����1
�����Ӆ���ԓ ����1
��������_�ԓ ����1
�����������I ����1
�������I ����1
ʒ���қ�C��I ����1
���޾���,��I ����1
���������I ����1
������t�ԓ ����1
��������k�ԓ ����1
գ��������ԓ ����1
��ר�ٳ���ԓ ����1
ෛ�������ԓ ����1
������Ʉ��ԓ ����1
"��ї�Z�ԓ ����1(Ȏ�������
#�򖐩�����ԓ ����1(Ȏ�������
#�ɕԺ����ԓ ����1(Ȏ�������
!������վN�ԓ ����1(��������'
"��������ԓ ����1(��������'
"��ڀ����ԓ ����1(��������'
!���䍟��B�ԓ ����1(��������'
"����̂呮�ԓ ����1(��������'
#������Ơ��ԓ ����1(��袺ص��
#�풠�����ԓ ����1(��袺ص��
!�����Ù��ԓ ����1(�ٴ�ڥ�7
!���������ԓ ����1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ ����1(�ٴ�ڥ�7
!������ڷu�ԓ ����1(�ٴ�ڥ�7�6�d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(����10��Z9 �          #38_h       k�l��,�   �_&   �_&
 �h�>]7; �          #38_h       OG�A<T   �_1   �_1	
Φ�hO��� �           O��� �           O��� �           O��� �           O��� �            GP�; �          #38_h       OG�A<T   �_�   �_�	
ߓ�i