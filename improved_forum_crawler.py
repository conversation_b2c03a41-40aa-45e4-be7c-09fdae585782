"""
Improved Multi-threaded Forum Crawler for LowEndTalk Flash Sales
Author: AI Assistant
Date: 2025-07-14

This script provides a robust, secure, and maintainable solution for monitoring
LowEndTalk forums for flash sale announcements with comprehensive error handling,
logging, configuration management, and anti-detection measures.
"""

import asyncio
import json
import re
import time
import os
import threading
import queue
import logging
import random
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from pathlib import Path
import hashlib
from concurrent.futures import ThreadPoolExecutor
import signal
import sys

from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings

# Redis imports (optional dependency)
try:
    import redis
    from redis.lock import Lock as RedisLock
    REDIS_AVAILABLE = True
except ImportError:
    redis = None
    RedisLock = None
    REDIS_AVAILABLE = False


@dataclass
class CrawlerConfig:
    """Configuration class for the forum crawler"""
    base_url: str = "https://lowendtalk.com/"
    monitor_url: str = "https://lowendtalk.com"
    state_file: str = "lowendtalk_crawl_state.json"
    results_file: str = "lowendtalk_flash_sales.json"
    
    # Flash sale detection keywords
    flash_sale_keywords: List[str] = field(default_factory=lambda: [
        "offer", "sale", "discount", "promo", "limited", "flash sale",
        "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth", "traffic",
        "$/month", "$/yr", "$/year", "$/mo", "price", "deal", "special", "promotion"
    ])
    
    # Threading configuration
    num_workers: int = 3  # Reduced to avoid browser resource issues
    monitor_timeout: int = 120  # Increased timeout for monitor ready
    worker_timeout: int = 60  # Increased worker timeout
    queue_timeout: int = 60  # Increased queue timeout
    
    # Browser configuration
    headless: bool = True
    user_agents: List[str] = field(default_factory=lambda: [
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:132.0) Gecko/20100101 Firefox/132.0",
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:132.0) Gecko/20100101 Firefox/132.0",
        "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
    ])
    
    # Timing configuration
    refresh_interval: int = 60
    scroll_delay: float = 2.0
    page_load_delay: float = 3.0
    
    # Rate limiting
    min_request_delay: float = 1.0
    max_request_delay: float = 3.0
    
    # Monitoring configuration
    max_posts_to_check: int = 10
    max_retries: int = 3

    # Processing optimization
    use_last_page_first: bool = True  # Enable last-page-first optimization

    # Redis distributed locking configuration
    redis_host: str = "localhost"
    redis_port: int = 6379
    redis_password: Optional[str] = None
    redis_db: int = 0
    redis_enabled: bool = False  # Enable Redis distributed locking
    redis_lock_timeout: int = 30  # Lock acquisition timeout in seconds
    redis_lock_expire: int = 300  # Lock expiration time in seconds (5 minutes)
    redis_connection_timeout: int = 5  # Redis connection timeout
    
    @classmethod
    def from_file(cls, config_path: str) -> 'CrawlerConfig':
        """Load configuration from JSON file"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)
            return cls(**config_data)
        except FileNotFoundError:
            logging.warning(f"Configuration file not found: {config_path}, using defaults")
            return cls()
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file: {e}")
            return cls()

    def load_from_file(self, config_path: str):
        """Load configuration from JSON file and update current instance"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config_data = json.load(f)

            # Update instance attributes with loaded data
            for key, value in config_data.items():
                if hasattr(self, key):
                    setattr(self, key, value)

        except FileNotFoundError:
            logging.warning(f"Configuration file not found: {config_path}, keeping defaults")
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in configuration file: {e}")
        except Exception as e:
            logging.error(f"Error loading configuration: {e}")


class CrawlerLogger:
    """Enhanced logging system for the crawler"""
    
    @staticmethod
    def setup_logging(log_level: str = "INFO", log_file: str = "forum_crawler.log") -> logging.Logger:
        """Setup comprehensive logging configuration"""
        logger = logging.getLogger("forum_crawler")
        logger.setLevel(getattr(logging, log_level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'
        )
        
        # Console handler
        console_handler = logging.StreamHandler()
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        # File handler with rotation
        from logging.handlers import RotatingFileHandler
        file_handler = RotatingFileHandler(
            log_file, maxBytes=10*1024*1024, backupCount=5
        )
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        return logger


class AntiDetectionManager:
    """Manages anti-detection measures"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.last_request_time = 0
        self.request_count = 0
        
    def get_random_user_agent(self) -> str:
        """Get a random user agent"""
        return random.choice(self.config.user_agents)
    
    def get_random_delay(self) -> float:
        """Get a random delay between requests"""
        return random.uniform(
            self.config.min_request_delay,
            self.config.max_request_delay
        )
    
    async def apply_rate_limiting(self) -> None:
        """Apply rate limiting between requests"""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time
        
        min_delay = self.get_random_delay()
        if time_since_last < min_delay:
            sleep_time = min_delay - time_since_last
            await asyncio.sleep(sleep_time)
        
        self.last_request_time = time.time()
        self.request_count += 1
    
    def should_take_break(self) -> bool:
        """Determine if we should take a longer break"""
        # Take a break every 50 requests
        return self.request_count > 0 and self.request_count % 50 == 0
    
    async def take_break(self) -> None:
        """Take a longer break to avoid detection"""
        break_time = random.uniform(30, 120)  # 30-120 seconds
        logging.info(f"Taking anti-detection break for {break_time:.1f} seconds")
        await asyncio.sleep(break_time)


class RedisLockManager:
    """Redis-based distributed lock manager with fallback to file-based locking"""

    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.redis_lock")
        self.redis_client = None
        self.fallback_lock = threading.RLock()
        self.redis_available = False

        # Initialize Redis connection if enabled
        if config.redis_enabled and REDIS_AVAILABLE:
            self._init_redis_connection()
        else:
            if config.redis_enabled and not REDIS_AVAILABLE:
                self.logger.warning("Redis locking requested but redis package not available. Falling back to file-based locking.")
            else:
                self.logger.info("Redis locking disabled. Using file-based locking.")

    def _init_redis_connection(self) -> None:
        """Initialize Redis connection with error handling"""
        try:
            self.redis_client = redis.Redis(
                host=self.config.redis_host,
                port=self.config.redis_port,
                password=self.config.redis_password,
                db=self.config.redis_db,
                socket_connect_timeout=self.config.redis_connection_timeout,
                socket_timeout=self.config.redis_connection_timeout,
                decode_responses=True,
                health_check_interval=30
            )

            # Test connection
            self.redis_client.ping()
            self.redis_available = True
            self.logger.info(f"Redis connection established: {self.config.redis_host}:{self.config.redis_port}")

        except Exception as e:
            self.logger.error(f"Failed to connect to Redis: {e}")
            self.logger.warning("Falling back to file-based locking")
            self.redis_client = None
            self.redis_available = False

    def acquire_lock(self, lock_name: str, timeout: Optional[int] = None) -> 'DistributedLock':
        """Acquire a distributed lock (Redis or fallback)"""
        if timeout is None:
            timeout = self.config.redis_lock_timeout

        return DistributedLock(
            lock_name=lock_name,
            redis_client=self.redis_client,
            fallback_lock=self.fallback_lock,
            timeout=timeout,
            expire=self.config.redis_lock_expire,
            logger=self.logger,
            redis_available=self.redis_available
        )


class DistributedLock:
    """Context manager for distributed locking with Redis fallback"""

    def __init__(self, lock_name: str, redis_client, fallback_lock: threading.RLock,
                 timeout: int, expire: int, logger, redis_available: bool):
        self.lock_name = lock_name
        self.redis_client = redis_client
        self.fallback_lock = fallback_lock
        self.timeout = timeout
        self.expire = expire
        self.logger = logger
        self.redis_available = redis_available
        self.redis_lock = None
        self.acquired = False

    def __enter__(self):
        """Acquire the lock"""
        if self.redis_available and self.redis_client:
            try:
                # Use Redis distributed lock
                lock_key = f"crawler:lock:{self.lock_name}"
                self.redis_lock = RedisLock(
                    self.redis_client,
                    lock_key,
                    timeout=self.timeout,
                    blocking_timeout=self.timeout,
                    thread_local=False
                )

                acquired = self.redis_lock.acquire(blocking=True, blocking_timeout=self.timeout)
                if acquired:
                    self.acquired = True
                    self.logger.debug(f"Acquired Redis lock: {lock_key}")
                    return self
                else:
                    self.logger.warning(f"Failed to acquire Redis lock: {lock_key}, falling back to local lock")
                    # Fall back to local lock
                    self.fallback_lock.acquire()
                    self.acquired = True
                    return self

            except Exception as e:
                self.logger.error(f"Redis lock error: {e}, falling back to local lock")
                # Fall back to local lock
                self.fallback_lock.acquire()
                self.acquired = True
                return self
        else:
            # Use fallback file-based lock
            self.fallback_lock.acquire()
            self.acquired = True
            self.logger.debug(f"Acquired fallback lock: {self.lock_name}")
            return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """Release the lock"""
        if self.acquired:
            try:
                if self.redis_lock:
                    try:
                        self.redis_lock.release()
                        self.logger.debug(f"Released Redis lock: {self.lock_name}")
                    except Exception as redis_error:
                        self.logger.warning(f"Redis lock release error: {redis_error}")
                else:
                    try:
                        self.fallback_lock.release()
                        self.logger.debug(f"Released fallback lock: {self.lock_name}")
                    except Exception as fallback_error:
                        # Only log if it's not an "un-acquired lock" error
                        if "un-acquired" not in str(fallback_error).lower():
                            self.logger.warning(f"Fallback lock release error: {fallback_error}")
            except Exception as e:
                self.logger.error(f"Error releasing lock {self.lock_name}: {e}")
            finally:
                self.acquired = False


class StateManager:
    """Thread-safe state management with Redis distributed locking"""

    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.lock_manager = RedisLockManager(config)
        self.logger = logging.getLogger("forum_crawler.state")
    
    def load_state(self) -> Dict[str, Any]:
        """Load crawler state from file with distributed locking and retry logic"""
        with self.lock_manager.acquire_lock("state"):
            return self._load_state_with_retry()

    def _load_state_with_retry(self, max_retries: int = 3) -> Dict[str, Any]:
        """Load state with retry logic for handling concurrent access"""
        for attempt in range(max_retries):
            try:
                if Path(self.config.state_file).exists():
                    with open(self.config.state_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if content.strip():  # Only parse if file has content
                            state = json.loads(content)
                            self.logger.debug(f"Loaded state with {len(state.get('processed_posts', {}))} processed posts")
                            return state
                        else:
                            self.logger.warning("State file is empty, returning default state")
                            return {"processed_posts": {}, "last_run": None}
                return {"processed_posts": {}, "last_run": None}
            except json.JSONDecodeError as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"JSON decode error on attempt {attempt + 1}: {e}, retrying...")
                    time.sleep(0.1)  # Short delay before retry
                    continue
                else:
                    self.logger.error(f"Failed to load state file after {max_retries} attempts: {e}")
                    return {"processed_posts": {}, "last_run": None}
            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Error loading state on attempt {attempt + 1}: {e}, retrying...")
                    time.sleep(0.1)
                    continue
                else:
                    self.logger.error(f"Failed to load state file after {max_retries} attempts: {e}")
                    return {"processed_posts": {}, "last_run": None}
    
    def save_state(self, state: Dict[str, Any]) -> None:
        """Save crawler state to file with distributed locking"""
        with self.lock_manager.acquire_lock("state"):
            try:
                state["last_run"] = datetime.now(timezone.utc).isoformat()

                # For concurrent access, use direct write with proper error handling
                # The distributed lock should prevent most race conditions
                import os
                import platform

                if platform.system() == "Windows":
                    # On Windows, use direct write since file locking is more complex
                    try:
                        with open(self.config.state_file, 'w', encoding='utf-8') as f:
                            json.dump(state, f, ensure_ascii=False, indent=2)
                    except OSError as e:
                        self.logger.warning(f"Direct write failed: {e}, trying temporary file approach")
                        # Fallback to temp file approach
                        temp_file = f"{self.config.state_file}.tmp.{os.getpid()}"
                        with open(temp_file, 'w', encoding='utf-8') as f:
                            json.dump(state, f, ensure_ascii=False, indent=2)

                        # Try to replace
                        try:
                            if os.path.exists(self.config.state_file):
                                os.remove(self.config.state_file)
                            os.rename(temp_file, self.config.state_file)
                        except OSError:
                            # Clean up temp file
                            if os.path.exists(temp_file):
                                os.remove(temp_file)
                            raise
                else:
                    # Unix systems: use atomic replace
                    temp_file = f"{self.config.state_file}.tmp"
                    with open(temp_file, 'w', encoding='utf-8') as f:
                        json.dump(state, f, ensure_ascii=False, indent=2)
                    os.replace(temp_file, self.config.state_file)

                self.logger.debug("State saved successfully")
            except Exception as e:
                self.logger.error(f"Failed to save state file: {e}")
                # Clean up temp file if it exists
                temp_file = f"{self.config.state_file}.tmp"
                if os.path.exists(temp_file):
                    try:
                        os.remove(temp_file)
                    except:
                        pass
    
    def load_results(self) -> List[Dict[str, Any]]:
        """Load previous flash sale results with distributed locking"""
        with self.lock_manager.acquire_lock("results"):
            results = self._load_results_with_retry()
            self.logger.debug(f"Loaded {len(results)} previous results")
            return results
    
    def save_results(self, new_results: List[Dict[str, Any]]) -> None:
        """Save flash sale results with deduplication and distributed locking"""
        with self.lock_manager.acquire_lock("results"):
            if not new_results:
                self.logger.info("No new results to save")
                return

            # Load existing results within the lock to ensure consistency
            existing_results = self._load_results_unlocked()

            # Create hash set for deduplication
            existing_hashes = {
                self._hash_result(result) for result in existing_results
            }

            new_items_added = 0
            for result in new_results:
                result_hash = self._hash_result(result)
                if result_hash not in existing_hashes:
                    existing_results.append(result)
                    existing_hashes.add(result_hash)
                    new_items_added += 1

            if new_items_added > 0:
                try:
                    # Use direct write with distributed locking for simplicity
                    with open(self.config.results_file, 'w', encoding='utf-8') as f:
                        json.dump(existing_results, f, ensure_ascii=False, indent=2)

                    self.logger.info(f"Saved {new_items_added} new flash sale results")
                except Exception as e:
                    self.logger.error(f"Failed to save results: {e}")
                    # Clean up temp file if it exists
                    temp_file = f"{self.config.results_file}.tmp"
                    if os.path.exists(temp_file):
                        try:
                            os.remove(temp_file)
                        except:
                            pass
            else:
                self.logger.info("No new unique results to save")

    def _load_results_unlocked(self) -> List[Dict[str, Any]]:
        """Load results without acquiring lock (for internal use within locked context)"""
        return self._load_results_with_retry()

    def _load_results_with_retry(self, max_retries: int = 3) -> List[Dict[str, Any]]:
        """Load results with retry logic for handling concurrent access"""
        for attempt in range(max_retries):
            try:
                if Path(self.config.results_file).exists():
                    with open(self.config.results_file, 'r', encoding='utf-8') as f:
                        content = f.read()
                        if content.strip():  # Only parse if file has content
                            results = json.loads(content)
                            return results
                        else:
                            self.logger.warning("Results file is empty, returning empty list")
                            return []
                return []
            except json.JSONDecodeError as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"JSON decode error loading results on attempt {attempt + 1}: {e}, retrying...")
                    time.sleep(0.1)  # Short delay before retry
                    continue
                else:
                    self.logger.error(f"Failed to load results file after {max_retries} attempts: {e}")
                    return []
            except Exception as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Error loading results on attempt {attempt + 1}: {e}, retrying...")
                    time.sleep(0.1)
                    continue
                else:
                    self.logger.error(f"Failed to load results file after {max_retries} attempts: {e}")
                    return []
    
    def _hash_result(self, result: Dict[str, Any]) -> str:
        """Create a hash for result deduplication"""
        # Use post_url and comment_id for uniqueness
        key = f"{result.get('post_url', '')}-{result.get('comment_id', '')}"
        return hashlib.md5(key.encode()).hexdigest()


class FlashSaleDetector:
    """Enhanced flash sale detection with improved accuracy"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.detector")
        
        # Compile regex patterns for better performance
        self.price_patterns = [
            re.compile(r'\$\d+(?:\.\d{2})?(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'\d+(?:\.\d{2})?\s*(?:usd|eur|gbp)(?:/(?:month|mo|year|yr))?', re.IGNORECASE),
            re.compile(r'(?:from|starting|only)\s*\$?\d+', re.IGNORECASE)
        ]
        
        self.spec_patterns = [
            re.compile(r'\d+\s*(?:gb|tb|mb)\s*(?:ram|memory|storage|disk)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:core|cpu|vcpu)', re.IGNORECASE),
            re.compile(r'\d+\s*(?:gb|tb)\s*(?:bandwidth|traffic)', re.IGNORECASE)
        ]
    
    def is_flash_sale(self, text: str) -> Dict[str, Any]:
        """Enhanced flash sale detection with confidence scoring"""
        if not text or len(text.strip()) < 10:
            return {"is_flash_sale": False, "confidence": 0.0, "reasons": []}
        
        text_lower = text.lower()
        reasons = []
        confidence = 0.0
        
        # Check for flash sale keywords
        keyword_matches = 0
        for keyword in self.config.flash_sale_keywords:
            if keyword in text_lower:
                keyword_matches += 1
                reasons.append(f"keyword: {keyword}")
        
        if keyword_matches > 0:
            confidence += min(keyword_matches * 0.1, 0.5)
        
        # Check for price patterns
        price_matches = 0
        for pattern in self.price_patterns:
            if pattern.search(text):
                price_matches += 1
                reasons.append("price_pattern")
        
        if price_matches > 0:
            confidence += min(price_matches * 0.2, 0.4)
        
        # Check for specification patterns
        spec_matches = 0
        for pattern in self.spec_patterns:
            if pattern.search(text):
                spec_matches += 1
                reasons.append("spec_pattern")
        
        if spec_matches > 0:
            confidence += min(spec_matches * 0.1, 0.3)
        
        # Boost confidence for certain combinations
        if "flash" in text_lower and "sale" in text_lower:
            confidence += 0.3
            reasons.append("flash_sale_combo")
        
        if "limited" in text_lower and ("time" in text_lower or "offer" in text_lower):
            confidence += 0.2
            reasons.append("limited_time_offer")
        
        # Reduce confidence for certain patterns that might be false positives
        if "sold out" in text_lower or "expired" in text_lower:
            confidence *= 0.5
            reasons.append("negative_indicator")
        
        is_flash_sale = confidence >= 0.3  # Threshold for classification
        
        return {
            "is_flash_sale": is_flash_sale,
            "confidence": min(confidence, 1.0),
            "reasons": reasons,
            "keyword_matches": keyword_matches,
            "price_matches": price_matches,
            "spec_matches": spec_matches
        }


class TimeUtils:
    """Utility functions for time parsing and handling"""
    
    @staticmethod
    def parse_time_string(time_str: Optional[str]) -> Optional[datetime]:
        """Parse various time string formats to datetime objects"""
        if not time_str:
            return None
        
        try:
            # Handle ISO format
            dt_obj = datetime.fromisoformat(time_str.replace('Z', '+00:00'))
            if dt_obj.tzinfo is None:
                return dt_obj.replace(tzinfo=timezone.utc)
            return dt_obj
        except ValueError:
            pass
        
        # Handle relative formats like "July 13"
        current_year = datetime.now().year
        formats_to_try = [
            f"%B %d, {current_year}",
            f"%B %d {current_year}",
            "%Y-%m-%d %H:%M:%S",
            "%Y-%m-%d"
        ]
        
        for fmt in formats_to_try:
            try:
                return datetime.strptime(time_str, fmt).replace(tzinfo=timezone.utc)
            except ValueError:
                continue
        
        logging.warning(f"Could not parse time string: {time_str}")
        return None


class BrowserManager:
    """Manages browser instances and tabs with proper resource cleanup"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.browser = None
        self.logger = logging.getLogger("forum_crawler.browser")
        self.anti_detection = AntiDetectionManager(config)
    
    def __enter__(self):
        """Context manager entry"""
        self.start_browser()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.stop_browser()
    
    def start_browser(self) -> None:
        """Initialize browser with enhanced anti-detection settings"""
        try:
            # Configure DrissionPage settings
            Settings.set_singleton_tab_obj(False)

            # Try to create browser with options, fallback to basic if needed
            try:
                # Create ChromiumOptions for better stealth
                from DrissionPage import ChromiumOptions

                options = ChromiumOptions()

                # Anti-detection arguments - use the correct API
                args = [
                    '--disable-blink-features=AutomationControlled',
                    '--disable-dev-shm-usage',
                    '--no-sandbox',
                    '--disable-gpu',
                    '--disable-extensions',
                    '--disable-plugins',
                    '--disable-images',  # Faster loading
                ]

                # Set a realistic user agent
                user_agent = self.anti_detection.get_random_user_agent()
                args.append(f'--user-agent={user_agent}')

                # Add arguments using the correct method
                for arg in args:
                    try:
                        options.add_argument(arg)
                    except AttributeError:
                        # If add_argument doesn't exist, try alternative methods
                        if hasattr(options, 'arguments'):
                            options.arguments.append(arg)
                        elif hasattr(options, 'add_arg'):
                            options.add_arg(arg)

                # Additional stealth settings
                try:
                    options.add_experimental_option("excludeSwitches", ["enable-automation"])
                    options.add_experimental_option('useAutomationExtension', False)
                except AttributeError:
                    pass  # Skip if method doesn't exist

                # Create browser with options
                self.browser = Chromium(addr_or_opts=options)
                self.logger.info(f"Browser started with user agent: {user_agent[:50]}...")

            except Exception as options_error:
                self.logger.warning(f"Failed to start browser with options: {options_error}")
                # Fallback to basic browser
                self.browser = Chromium()
                self.logger.info("Browser started with basic configuration")

        except Exception as e:
            self.logger.error(f"Failed to start browser: {e}")
            raise
    
    def stop_browser(self) -> None:
        """Safely stop browser and cleanup resources"""
        if self.browser:
            try:
                self.browser.quit()
                self.logger.info("Browser stopped successfully")
            except Exception as e:
                self.logger.error(f"Error stopping browser: {e}")
    
    def create_tab(self) -> Optional[ChromiumPage]:
        """Create a new browser tab with error handling"""
        try:
            if not self.browser:
                raise RuntimeError("Browser not initialized")

            tab = self.browser.new_tab()

            # Apply anti-detection measures if the method exists
            try:
                if hasattr(tab, 'set') and hasattr(tab.set, 'user_agent'):
                    tab.set.user_agent(self.anti_detection.get_random_user_agent())
                elif hasattr(tab, 'set_user_agent'):
                    tab.set_user_agent(self.anti_detection.get_random_user_agent())
            except Exception as ua_error:
                self.logger.warning(f"Could not set user agent: {ua_error}")

            return tab
        except Exception as e:
            self.logger.error(f"Failed to create browser tab: {e}")
            return None


class HealthMonitor:
    """Monitors the health of crawler components"""
    
    def __init__(self):
        self.thread_health = {}
        self.last_activity = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger("forum_crawler.health")
    
    def update_thread_health(self, thread_id: str, status: str, details: str = "") -> None:
        """Update thread health status"""
        with self.lock:
            self.thread_health[thread_id] = {
                "status": status,
                "details": details,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            self.last_activity[thread_id] = time.time()
    
    def check_thread_health(self, max_inactive_time: int = 300) -> Dict[str, Any]:
        """Check health of all threads"""
        with self.lock:
            current_time = time.time()
            health_report = {
                "healthy_threads": [],
                "unhealthy_threads": [],
                "inactive_threads": []
            }
            
            for thread_id, last_time in self.last_activity.items():
                time_since_activity = current_time - last_time
                thread_status = self.thread_health.get(thread_id, {})
                
                if time_since_activity > max_inactive_time:
                    health_report["inactive_threads"].append({
                        "thread_id": thread_id,
                        "inactive_time": time_since_activity,
                        "last_status": thread_status
                    })
                elif thread_status.get("status") == "error":
                    health_report["unhealthy_threads"].append({
                        "thread_id": thread_id,
                        "status": thread_status
                    })
                else:
                    health_report["healthy_threads"].append(thread_id)
            
            return health_report
    
    def log_health_summary(self) -> None:
        """Log a summary of system health"""
        health = self.check_thread_health()
        self.logger.info(f"Health Summary - Healthy: {len(health['healthy_threads'])}, "
                        f"Unhealthy: {len(health['unhealthy_threads'])}, "
                        f"Inactive: {len(health['inactive_threads'])}")


# Global instances for shared resources
health_monitor = HealthMonitor()
shutdown_event = threading.Event()


def signal_handler(signum, frame):
    """Handle shutdown signals gracefully"""
    logging.info(f"Received signal {signum}, initiating graceful shutdown...")
    shutdown_event.set()


# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


class ForumCrawler:
    """Main crawler class orchestrating all components"""
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("forum_crawler.main")
        self.state_manager = StateManager(config)
        self.detector = FlashSaleDetector(config)
        
        # Thread synchronization
        self.task_queue = queue.Queue()
        self.monitor_ready_event = threading.Event()
        self.all_flash_sales_found = []
        self.results_lock = threading.Lock()
        
        # Thread management
        self.worker_threads = []
        self.monitor_thread = None
        
    def start(self) -> None:
        """Start the crawler with all components"""
        self.logger.info("Starting Forum Crawler...")
        
        try:
            with BrowserManager(self.config) as browser_manager:
                self._start_threads(browser_manager)
                self._run_main_loop()
        except Exception as e:
            self.logger.error(f"Critical error in crawler: {e}")
        finally:
            self._cleanup()
    
    def _start_threads(self, browser_manager: BrowserManager) -> None:
        """Start monitor and worker threads"""
        # Start monitor thread
        self.monitor_thread = threading.Thread(
            target=self._monitor_thread_func,
            args=(browser_manager,),
            name="Monitor",
            daemon=True
        )
        self.monitor_thread.start()
        
        # Start worker threads
        for i in range(self.config.num_workers):
            worker_thread = threading.Thread(
                target=self._worker_thread_func,
                args=(f"Worker-{i+1}", browser_manager),
                name=f"Worker-{i+1}",
                daemon=True
            )
            worker_thread.start()
            self.worker_threads.append(worker_thread)
        
        self.logger.info(f"Started {self.config.num_workers} worker threads and 1 monitor thread")
    
    def _run_main_loop(self) -> None:
        """Main loop for monitoring and health checks"""
        health_check_interval = 60  # Check health every minute
        queue_check_interval = 30   # Check queue status every 30 seconds
        last_health_check = 0
        last_queue_check = 0

        while not shutdown_event.is_set():
            try:
                current_time = time.time()

                # Periodic health checks
                if current_time - last_health_check > health_check_interval:
                    health_monitor.log_health_summary()
                    self._log_system_status()
                    last_health_check = current_time

                # Periodic queue status checks
                if current_time - last_queue_check > queue_check_interval:
                    self._log_queue_status()
                    last_queue_check = current_time

                time.sleep(1)

            except KeyboardInterrupt:
                self.logger.info("Received keyboard interrupt")
                break
            except Exception as e:
                self.logger.error(f"Error in main loop: {e}")
                time.sleep(5)

    def _log_system_status(self) -> None:
        """Log comprehensive system status"""
        try:
            # Count active threads
            active_workers = sum(1 for t in self.worker_threads if t.is_alive())
            monitor_alive = self.monitor_thread.is_alive() if self.monitor_thread else False

            # Use ASCII characters to avoid encoding issues
            monitor_status = "OK" if monitor_alive else "FAILED"
            self.logger.info(f"System Status - Monitor: {monitor_status}, "
                           f"Workers: {active_workers}/{len(self.worker_threads)} active")

        except Exception as e:
            self.logger.error(f"Error logging system status: {e}")

    def _log_queue_status(self) -> None:
        """Log queue status and worker activity"""
        try:
            queue_size = self.task_queue.qsize()
            if queue_size > 0:
                self.logger.info(f"QUEUE Status: {queue_size} tasks waiting for processing")
            else:
                self.logger.debug("Queue Status: Empty")
        except Exception as e:
            self.logger.error(f"Error logging queue status: {e}")
    
    def _cleanup(self) -> None:
        """Cleanup resources and save final state"""
        self.logger.info("Starting cleanup process...")
        
        # Signal shutdown to all threads
        shutdown_event.set()
        
        # Wait for threads to finish
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)
        
        for thread in self.worker_threads:
            if thread.is_alive():
                thread.join(timeout=10)
        
        # Save final results
        with self.results_lock:
            if self.all_flash_sales_found:
                self.state_manager.save_results(self.all_flash_sales_found)
                self.logger.info(f"Saved {len(self.all_flash_sales_found)} flash sale results")
        
        # Save final state
        final_state = self.state_manager.load_state()
        self.state_manager.save_state(final_state)
        
        self.logger.info("Cleanup completed")
    
    def _monitor_thread_func(self, browser_manager: BrowserManager) -> None:
        """Monitor thread function - tracks forum for updates"""
        thread_id = "monitor"
        health_monitor.update_thread_health(thread_id, "starting", "Initializing monitor thread")

        try:
            monitor_page = browser_manager.create_tab()
            if not monitor_page:
                raise RuntimeError("Failed to create monitor page")

            # Perform initial scan with timeout protection
            try:
                self._perform_initial_scan(monitor_page, thread_id)
                self.monitor_ready_event.set()
                self.logger.info("Monitor thread ready event set - workers can now start")
            except Exception as e:
                self.logger.error(f"Initial scan failed: {e}")
                # Set the event anyway to prevent workers from hanging
                self.monitor_ready_event.set()
                raise

            # Continuous monitoring loop
            while not shutdown_event.is_set():
                try:
                    self._perform_monitoring_cycle(monitor_page, thread_id)
                    time.sleep(self.config.refresh_interval)
                except Exception as e:
                    self.logger.error(f"Error in monitoring cycle: {e}")
                    health_monitor.update_thread_health(thread_id, "error", str(e))
                    time.sleep(30)  # Wait before retrying

        except Exception as e:
            self.logger.error(f"Critical error in monitor thread: {e}")
            health_monitor.update_thread_health(thread_id, "critical_error", str(e))
            # Always set the ready event to prevent workers from hanging
            self.monitor_ready_event.set()
        finally:
            health_monitor.update_thread_health(thread_id, "stopped", "Monitor thread ended")
            self.logger.info("Monitor thread ended")
    
    def _perform_initial_scan(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform initial scan of the forum"""
        health_monitor.update_thread_health(thread_id, "initial_scan", "Loading forum page")

        try:
            self.logger.info(f"Loading forum page: {self.config.monitor_url}")
            monitor_page.get(self.config.monitor_url)

            # Wait for page to load
            self.logger.info("Waiting for page to load...")
            time.sleep(self.config.page_load_delay)

            # Try multiple selectors to find posts container
            posts_container = None
            selectors_to_try = [
                'tag:ul@class=DataList Discussions',
                '.DataList.Discussions',
                'ul.DataList.Discussions',
                '.ItemDiscussion'  # Try to find individual posts if container not found
            ]

            for selector in selectors_to_try:
                self.logger.info(f"Trying selector: {selector}")
                try:
                    posts_container = monitor_page.ele(selector, timeout=10)
                    if posts_container:
                        self.logger.info(f"Found posts container with selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not posts_container:
                # Log page content for debugging
                self.logger.error("Could not find posts container. Page title: " +
                                (monitor_page.title if hasattr(monitor_page, 'title') else "Unknown"))

                # Try a more basic approach - just look for any posts
                try:
                    # Check if we can find any discussion items at all
                    any_posts = monitor_page.eles('.ItemDiscussion')
                    if any_posts:
                        self.logger.info(f"Found {len(any_posts)} individual posts, proceeding without container")
                        # Create a mock container for processing
                        posts_container = monitor_page  # Use the page itself as container
                    else:
                        raise RuntimeError("No posts found on page - site may be down or structure changed")
                except Exception as e:
                    self.logger.error(f"Failed to find any posts: {e}")
                    raise RuntimeError("Could not find any posts on the forum page")

            health_monitor.update_thread_health(thread_id, "scanning", "Processing initial posts")
            self._process_posts(posts_container, thread_id, is_initial=True)

            health_monitor.update_thread_health(thread_id, "ready", "Initial scan completed")
            self.logger.info("Initial scan completed, monitor thread ready")

        except Exception as e:
            self.logger.error(f"Error in initial scan: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Initial scan failed: {e}")
            raise
    
    def _perform_monitoring_cycle(self, monitor_page: ChromiumPage, thread_id: str) -> None:
        """Perform a single monitoring cycle"""
        health_monitor.update_thread_health(thread_id, "refreshing", "Refreshing forum page")
        
        monitor_page.refresh()
        time.sleep(self.config.page_load_delay)
        
        posts_container = monitor_page.ele('tag:ul@class=DataList Discussions', timeout=30)
        if not posts_container:
            self.logger.warning("Could not find posts container after refresh")
            return
        
        health_monitor.update_thread_health(thread_id, "processing", "Processing updated posts")
        self._process_posts(posts_container, thread_id, is_initial=False)
        
        health_monitor.update_thread_health(thread_id, "active", "Monitoring cycle completed")
    
    def _process_posts(self, posts_container, thread_id: str, is_initial: bool = False) -> None:
        """Process posts from the container"""
        try:
            # Get all discussion items - try multiple approaches
            all_items = []

            # Try different selectors to find discussion items
            # Based on debug analysis: actual classes are "Item Unread ItemDiscussion ItemDiscussion-withPhoto"
            selectors_to_try = [
                'xpath:./li[contains(@class, "Item")]',  # Direct children with Item class
                'li.Item',  # Li elements with Item class
                'xpath:.//li[contains(@class, "ItemDiscussion")]',  # Fallback to original
                '.Item'  # Any element with Item class
            ]

            for selector in selectors_to_try:
                try:
                    items = posts_container.eles(selector)
                    if items:
                        all_items = items
                        self.logger.info(f"Found {len(all_items)} items using selector: {selector}")
                        break
                except Exception as e:
                    self.logger.warning(f"Selector {selector} failed: {e}")
                    continue

            if not all_items:
                self.logger.warning("No discussion items found, trying fallback approach")
                # Fallback: try to find any items that might be posts
                try:
                    all_items = posts_container.eles('li')
                    self.logger.info(f"Fallback found {len(all_items)} li elements")
                except Exception as e:
                    self.logger.error(f"Fallback approach failed: {e}")
                    return

            # Filter out announcements - use faster approach
            non_announcement_posts = []
            for i, item in enumerate(all_items):
                try:
                    # Use a faster method - check if class contains announcement
                    item_classes = item.attr('class') or ''
                    if 'announcement' not in item_classes.lower():
                        non_announcement_posts.append(item)
                        if i < 5:  # Log first few for debugging
                            self.logger.debug(f"Item {i+1} classes: {item_classes}")
                    else:
                        self.logger.debug(f"Skipping announcement item {i+1}")
                except Exception as e:
                    # If we can't check for announcements, include the item anyway
                    self.logger.warning(f"Could not check announcement status for item {i+1}: {e}")
                    non_announcement_posts.append(item)

            # Limit posts to check
            posts_to_check = non_announcement_posts[:self.config.max_posts_to_check]

            scan_type = "Initial" if is_initial else "Update"
            self.logger.info(f"{scan_type} scan found {len(posts_to_check)} posts to check")

            if not posts_to_check:
                self.logger.warning("No posts to check after filtering")
                return

            # Load current state
            self.logger.info("Loading current state...")
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            self.logger.info(f"Loaded state with {len(processed_posts)} previously processed posts")

            for i, item in enumerate(posts_to_check):
                try:
                    self.logger.info(f"Processing post {i+1}/{len(posts_to_check)}")
                    post_data = self._extract_post_data(item)
                    if not post_data:
                        self.logger.warning(f"Could not extract data from post {i+1}")
                        continue

                    safe_title = post_data['post_title'][:50].encode('ascii', 'ignore').decode('ascii')
                    self.logger.info(f"Extracted post: {safe_title}...")
                    should_queue = self._should_process_post(post_data, processed_posts)

                    if should_queue:
                        self.task_queue.put(post_data)
                        queue_size = self.task_queue.qsize()
                        self.logger.info(f"QUEUED post: {safe_title}... (Queue size: {queue_size})")
                    else:
                        # Update state for unchanged posts
                        self._update_post_state(post_data, processed_posts)
                        self.logger.debug(f"Skipped unchanged post: {post_data['post_title'][:50]}...")

                except Exception as e:
                    self.logger.error(f"Error processing post item {i+1}: {e}")
                    continue

            # Save updated state
            self.logger.info("Saving updated state...")
            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

            # Log final queue status
            final_queue_size = self.task_queue.qsize()
            self.logger.info(f"Post processing completed for {scan_type} scan. Final queue size: {final_queue_size}")

        except Exception as e:
            self.logger.error(f"Error in _process_posts: {e}")
            health_monitor.update_thread_health(thread_id, "error", f"Post processing error: {e}")
            raise  # Re-raise to ensure the error is handled properly

    def _extract_post_data(self, item) -> Optional[Dict[str, Any]]:
        """Extract post data from a forum item"""
        try:
            # Use the working approach from quick_fix_crawler.py
            # Try to find title link directly (this works!)
            title_link = item.ele('a', timeout=2)
            if not title_link:
                self.logger.debug("No title link found")
                return None

            post_title = title_link.text.strip()
            post_url = title_link.link

            if not post_title or not post_url:
                self.logger.debug("Missing title or URL")
                return None

            # Ensure absolute URL
            if not post_url.startswith('http'):
                post_url = self.config.base_url.rstrip('/') + post_url

            # Extract comment count using the working method
            current_comment_count = 0
            try:
                # Look for comment count in various places
                count_elements = item.eles('@@class:Number@@title:comments')
                for elem in count_elements:
                    text = elem.text.strip()
                    if text.isdigit():
                        current_comment_count = int(text)
                        break
                    elif 'k' in text.lower():
                        try:
                            num = float(text.lower().replace('k', ''))
                            current_comment_count = int(num * 1000)
                            break
                        except:
                            pass
            except Exception as e:
                self.logger.debug(f"Could not extract comment count: {e}")

            # Extract last comment date - simplified approach
            datetime_attr = None
            try:
                time_elements = item.eles('tag:time')
                if time_elements:
                    datetime_attr = time_elements[0].attr('datetime')
            except Exception as e:
                self.logger.debug(f"Could not extract datetime: {e}")

            # If we got here, we have at least title and URL
            # Use safe logging to avoid Unicode errors
            safe_title = post_title[:50].encode('ascii', 'ignore').decode('ascii')
            self.logger.info(f"Extracted: {safe_title}... (Comments: {current_comment_count})")

            return {
                "post_url": post_url,
                "post_title": post_title,
                "current_comment_count": current_comment_count,
                "datetime_attr": datetime_attr or datetime.now(timezone.utc).isoformat()
            }

        except Exception as e:
            self.logger.error(f"Error extracting post data: {e}")
            return None

    def _should_process_post(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> bool:
        """Determine if a post should be processed"""
        post_url = post_data["post_url"]
        current_comment_count = post_data["current_comment_count"]
        datetime_attr = post_data["datetime_attr"]

        if post_url not in processed_posts:
            self.logger.debug(f"New post detected: {post_data['post_title'][:50]}...")
            return True

        prev_state = processed_posts[post_url]
        prev_comment_count = prev_state.get("last_comment_count", 0)
        prev_datetime_str = prev_state.get("last_comment_datetime")

        # Check comment count increase
        if current_comment_count > prev_comment_count:
            self.logger.debug(f"Comment count increased: {prev_comment_count} -> {current_comment_count}")
            return True

        # Check timestamp update
        if datetime_attr and prev_datetime_str:
            current_datetime = TimeUtils.parse_time_string(datetime_attr)
            prev_datetime = TimeUtils.parse_time_string(prev_datetime_str)

            if current_datetime and prev_datetime and current_datetime > prev_datetime:
                self.logger.debug(f"Timestamp updated: {prev_datetime_str} -> {datetime_attr}")
                return True

        return False

    def _update_post_state(self, post_data: Dict[str, Any], processed_posts: Dict[str, Any]) -> None:
        """Update state for posts that don't need processing"""
        post_url = post_data["post_url"]
        prev_state = processed_posts.get(post_url, {})

        processed_posts[post_url] = {
            "last_comment_count": post_data["current_comment_count"],
            "last_comment_datetime": post_data["datetime_attr"],
            "processed_comment_ids": prev_state.get("processed_comment_ids", [])
        }

    def _worker_thread_func(self, worker_id: str, browser_manager: BrowserManager) -> None:
        """Worker thread function - processes posts from the queue"""
        health_monitor.update_thread_health(worker_id, "starting", "Initializing worker thread")
        self.logger.info(f"[{worker_id}] Worker thread starting...")

        # Initialize task_count outside try block to avoid UnboundLocalError
        task_count = 0

        try:
            # Create browser tab with retry logic
            worker_page = None
            for attempt in range(3):
                try:
                    worker_page = browser_manager.create_tab()
                    if worker_page:
                        self.logger.info(f"[{worker_id}] Successfully created browser tab on attempt {attempt + 1}")
                        break
                    else:
                        self.logger.warning(f"[{worker_id}] Failed to create browser tab on attempt {attempt + 1}")
                        time.sleep(2)
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error creating browser tab on attempt {attempt + 1}: {e}")
                    time.sleep(2)

            if not worker_page:
                raise RuntimeError(f"[{worker_id}] Failed to create worker page after 3 attempts")

            # Wait for monitor to be ready with extended timeout
            self.logger.info(f"[{worker_id}] Waiting for monitor thread (timeout: {self.config.monitor_timeout}s)...")
            if not self.monitor_ready_event.wait(timeout=self.config.monitor_timeout):
                self.logger.error(f"[{worker_id}] Monitor thread not ready after {self.config.monitor_timeout}s, worker exiting")
                return

            self.logger.info(f"[{worker_id}] Monitor ready! Worker starting main processing loop...")
            health_monitor.update_thread_health(worker_id, "ready", "Worker ready to process tasks")

            # Main processing loop with enhanced logging
            task_count = 0
            while not shutdown_event.is_set():
                try:
                    # Log queue status periodically
                    if task_count % 10 == 0:
                        queue_size = self.task_queue.qsize()
                        self.logger.info(f"[{worker_id}] Queue status: {queue_size} tasks waiting, processed {task_count} tasks so far")

                    # Get task from queue with timeout
                    self.logger.debug(f"[{worker_id}] Waiting for task from queue...")
                    task_data = self.task_queue.get(timeout=self.config.queue_timeout)

                    task_count += 1
                    safe_title = task_data['post_title'][:30].encode('ascii', 'ignore').decode('ascii')
                    self.logger.info(f"[{worker_id}] Got task #{task_count}: {safe_title}...")

                    health_monitor.update_thread_health(worker_id, "processing", f"Processing task #{task_count}: {safe_title}...")

                    # Process the post
                    self._process_post_comments(worker_id, worker_page, task_data)

                    self.task_queue.task_done()
                    health_monitor.update_thread_health(worker_id, "active", f"Task #{task_count} completed")
                    self.logger.info(f"[{worker_id}] Completed task #{task_count}: {safe_title}...")

                except queue.Empty:
                    # No tasks available, continue waiting
                    health_monitor.update_thread_health(worker_id, "waiting", "Waiting for tasks")
                    self.logger.debug(f"[{worker_id}] No tasks in queue, continuing to wait...")
                    continue
                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing task #{task_count}: {e}")
                    health_monitor.update_thread_health(worker_id, "error", str(e))

                    # Try to recover by recreating the page
                    try:
                        self.logger.info(f"[{worker_id}] Attempting to recover browser tab...")
                        if worker_page:
                            worker_page.close()
                        worker_page = browser_manager.create_tab()
                        if worker_page:
                            self.logger.info(f"[{worker_id}] Successfully recovered browser tab")
                        else:
                            self.logger.error(f"[{worker_id}] Failed to recover browser tab")
                            break
                    except Exception as recovery_error:
                        self.logger.error(f"[{worker_id}] Failed to recover: {recovery_error}")
                        break

                    # Always call task_done even on error
                    try:
                        self.task_queue.task_done()
                    except ValueError:
                        pass  # task_done() called more times than get()

        except Exception as e:
            self.logger.error(f"[{worker_id}] Critical error in worker thread: {e}")
            health_monitor.update_thread_health(worker_id, "critical_error", str(e))
        finally:
            # Cleanup
            try:
                if worker_page:
                    worker_page.close()
            except Exception as cleanup_error:
                self.logger.warning(f"[{worker_id}] Error during cleanup: {cleanup_error}")

            health_monitor.update_thread_health(worker_id, "stopped", "Worker thread ended")
            self.logger.info(f"[{worker_id}] Worker thread ended after processing {task_count} tasks")

    def _calculate_page_for_comment(self, comment_number: int, comments_per_page: int = 30) -> int:
        """Calculate which page a specific comment number is on for LowEndTalk"""
        if comment_number <= 0:
            return 1
        # LowEndTalk has 30 comments per page, page numbers start from 1
        return ((comment_number - 1) // comments_per_page) + 1

    def _calculate_last_page(self, total_comments: int, comments_per_page: int = 30) -> int:
        """Calculate the last page number based on total comment count"""
        if total_comments <= 0:
            return 1
        # Use ceiling division to get the last page
        import math
        return math.ceil(total_comments / comments_per_page)

    def _build_paginated_url(self, base_url: str, page_number: int) -> str:
        """Build LowEndTalk paginated URL with correct format"""
        if page_number <= 1:
            return base_url  # Page 1 has no suffix
        else:
            # Remove any existing pagination from URL
            if '/p' in base_url:
                base_url = base_url.split('/p')[0]
            paginated_url = f"{base_url}/p{page_number}"
            self.logger.debug(f"Built paginated URL: {paginated_url}")
            return paginated_url

    def _build_last_page_url(self, base_url: str, total_comments: int) -> tuple[str, int]:
        """Build URL for the last page of comments and return the page number"""
        last_page = self._calculate_last_page(total_comments)
        last_page_url = self._build_paginated_url(base_url, last_page)
        return last_page_url, last_page

    def _process_post_comments(self, worker_id: str, worker_page: ChromiumPage, task_data: Dict[str, Any]) -> None:
        """Process comments in a post for flash sales with optimized last-page-first navigation"""
        post_url = task_data["post_url"]
        post_title = task_data["post_title"]
        current_comment_count = task_data["current_comment_count"]
        datetime_attr = task_data["datetime_attr"]

        safe_title = post_title.encode('ascii', 'ignore').decode('ascii')
        self.logger.info(f"[{worker_id}] Processing: {safe_title}")

        try:
            # Get previously processed comment IDs and determine starting point
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})
            prev_state = processed_posts.get(post_url, {})
            prev_processed_ids = set(prev_state.get("processed_comment_ids", []))
            prev_comment_count = prev_state.get("last_comment_count", 0)

            # Determine processing strategy
            is_new_post = post_url not in processed_posts

            if is_new_post:
                # For new posts, apply minimum comment threshold
                if current_comment_count < 5:
                    self.logger.info(f"[{worker_id}] Skipping new post with only {current_comment_count} comments (below threshold)")
                    # Still update the state even if we skip processing
                    self._update_processed_post_state(post_url, current_comment_count, datetime_attr, set())
                    return

                # Choose processing strategy based on configuration
                if self.config.use_last_page_first:
                    self.logger.info(f"[{worker_id}] Processing new post with {current_comment_count} comments using last-page-first strategy")
                    flash_sales_found = self._process_comments_last_page_first(
                        worker_id, worker_page, post_url, post_title, current_comment_count,
                        prev_processed_ids, is_new_post=True
                    )
                else:
                    self.logger.info(f"[{worker_id}] Processing new post with {current_comment_count} comments using traditional strategy")
                    flash_sales_found = self._process_comments_traditional(
                        worker_id, worker_page, post_url, post_title, current_comment_count,
                        prev_processed_ids, start_comment_number=1
                    )
            else:
                # For existing posts with new comments, use optimized incremental processing
                start_comment_number = prev_comment_count + 1
                self.logger.info(f"[{worker_id}] Incremental processing: "
                               f"prev_count={prev_comment_count}, current_count={current_comment_count}, "
                               f"start_comment={start_comment_number}")

                # Choose processing strategy based on configuration
                if self.config.use_last_page_first:
                    flash_sales_found = self._process_comments_last_page_first(
                        worker_id, worker_page, post_url, post_title, current_comment_count,
                        prev_processed_ids, is_new_post=False, start_comment_number=start_comment_number
                    )
                else:
                    flash_sales_found = self._process_comments_traditional(
                        worker_id, worker_page, post_url, post_title, current_comment_count,
                        prev_processed_ids, start_comment_number=start_comment_number
                    )

            # Update results and state
            if flash_sales_found:
                with self.results_lock:
                    self.all_flash_sales_found.extend(flash_sales_found)

                self.logger.info(f"[{worker_id}] Found {len(flash_sales_found)} flash sales in this post")
            else:
                self.logger.info(f"[{worker_id}] No flash sales found in this post")

            # CRITICAL: Always update post state to reflect processed comments
            # For optimized processing, we consider all comments up to current_comment_count as processed
            # This is safe because we're focusing on the most recent comments (last page)
            if self.config.use_last_page_first:
                # For last-page-first, we mark all comments as processed since we got the latest ones
                all_processed_ids = prev_processed_ids.union({fs.get('comment_id') for fs in flash_sales_found if fs.get('comment_id')})
                self.logger.info(f"[{worker_id}] Last-page-first: Updating state comment_count {prev_comment_count} -> {current_comment_count}")
            else:
                # For traditional processing, only mark actually processed comments
                all_processed_ids = prev_processed_ids.union({fs.get('comment_id') for fs in flash_sales_found if fs.get('comment_id')})
                self.logger.info(f"[{worker_id}] Traditional: Updating state comment_count {prev_comment_count} -> {current_comment_count}, "
                               f"processed_ids: {len(prev_processed_ids)} -> {len(all_processed_ids)}")

            self._update_processed_post_state(post_url, current_comment_count, datetime_attr, all_processed_ids)

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing post comments: {e}")
            raise

            while current_page <= max_page and not shutdown_event.is_set():
                try:
                    # Build paginated URL
                    paginated_url = self._build_paginated_url(post_url, current_page)

                    self.logger.info(f"[{worker_id}] Processing page {current_page}/{max_page}: {paginated_url}")

                    # Navigate to the specific page
                    worker_page.get(paginated_url)
                    worker_page.wait.load_start()

                    # Apply anti-detection delay
                    time.sleep(random.uniform(1, 3))

                    # Get comments on this page
                    comments = worker_page.eles('.Comment')
                    self.logger.info(f"[{worker_id}] Found {len(comments)} comments on page {current_page}")

                    if not comments:
                        self.logger.warning(f"[{worker_id}] No comments found on page {current_page}")
                        # Check if this is a valid page by looking for pagination indicators
                        pagination_elements = worker_page.eles('.Pager')
                        if not pagination_elements and current_page > 1:
                            self.logger.info(f"[{worker_id}] Page {current_page} appears to be beyond available pages, stopping")
                            break
                        else:
                            self.logger.info(f"[{worker_id}] Page {current_page} exists but has no comments, continuing")
                            current_page += 1
                            continue

                    # Process comments on this page
                    page_flash_sales = self._process_comments_on_page(
                        worker_id, comments, prev_processed_ids, new_processed_ids,
                        post_title, post_url, start_comment_number, current_page
                    )
                    flash_sales_found.extend(page_flash_sales)

                    current_page += 1

                except Exception as e:
                    self.logger.error(f"[{worker_id}] Error processing page {current_page}: {e}")
                    current_page += 1
                    continue

            # Update results and state
            if flash_sales_found:
                with self.results_lock:
                    self.all_flash_sales_found.extend(flash_sales_found)

                self.logger.info(f"[{worker_id}] Found {len(flash_sales_found)} flash sales in this post")
            else:
                self.logger.info(f"[{worker_id}] No flash sales found in this post")

            # CRITICAL: Always update post state to reflect processed comments
            all_processed_ids = prev_processed_ids.union(new_processed_ids)
            self.logger.info(f"[{worker_id}] Updating state: comment_count {prev_comment_count} -> {current_comment_count}, "
                           f"processed_ids: {len(prev_processed_ids)} -> {len(all_processed_ids)}")

            self._update_processed_post_state(post_url, current_comment_count, datetime_attr, all_processed_ids)

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error processing post comments: {e}")
            raise

    def _process_comments_last_page_first(self, worker_id: str, worker_page: ChromiumPage,
                                        post_url: str, post_title: str, current_comment_count: int,
                                        prev_processed_ids: Set[str], is_new_post: bool = False,
                                        start_comment_number: int = 1) -> List[Dict[str, Any]]:
        """Process comments using last-page-first strategy for optimal performance"""
        flash_sales_found = []
        new_processed_ids = set()

        # Calculate the last page and build URL
        last_page_url, last_page_number = self._build_last_page_url(post_url, current_comment_count)

        self.logger.info(f"[{worker_id}] Using last-page-first strategy: "
                       f"total_comments={current_comment_count}, last_page={last_page_number}")
        self.logger.info(f"[{worker_id}] Navigating directly to last page: {last_page_url}")

        try:
            # Navigate directly to the last page
            worker_page.get(last_page_url)
            worker_page.wait.load_start()

            # Apply anti-detection delay
            time.sleep(random.uniform(1, 3))

            # Get comments on the last page
            comments = worker_page.eles('.Comment')
            self.logger.info(f"[{worker_id}] Found {len(comments)} comments on last page {last_page_number}")

            if not comments:
                self.logger.warning(f"[{worker_id}] No comments found on last page {last_page_number}")
                return flash_sales_found

            # Process comments on the last page
            if is_new_post:
                # For new posts, process all comments on the last page
                page_flash_sales = self._process_comments_on_page(
                    worker_id, comments, prev_processed_ids, new_processed_ids,
                    post_title, post_url, start_comment_number, last_page_number
                )
                flash_sales_found.extend(page_flash_sales)

                # If this is a new post with multiple pages, we might want to process more pages
                # But for optimization, we focus on the most recent comments (last page)
                if last_page_number > 1:
                    self.logger.info(f"[{worker_id}] New post has {last_page_number} pages, "
                                   f"processed last page for most recent comments")
            else:
                # For incremental processing, only process new comments
                page_flash_sales = self._process_comments_on_page_incremental(
                    worker_id, comments, prev_processed_ids, new_processed_ids,
                    post_title, post_url, start_comment_number, last_page_number
                )
                flash_sales_found.extend(page_flash_sales)

            self.logger.info(f"[{worker_id}] Last-page-first processing completed: "
                           f"processed {len(new_processed_ids)} new comments, "
                           f"found {len(flash_sales_found)} flash sales")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error in last-page-first processing: {e}")
            raise

        return flash_sales_found

    def _process_comments_on_page(self, worker_id: str, comments: List, prev_processed_ids: Set[str],
                                new_processed_ids: Set[str], post_title: str, post_url: str,
                                start_comment_number: int, current_page: int) -> List[Dict[str, Any]]:
        """Process comments on a specific page, filtering for new comments only"""
        flash_sales_found = []

        for i, comment_element in enumerate(comments):
            try:
                comment_id = comment_element.attr('id')
                if not comment_id:
                    continue

                # Skip if already processed
                if comment_id in prev_processed_ids or comment_id in new_processed_ids:
                    continue

                # Extract comment number from ID if possible (for better filtering)
                comment_number = self._extract_comment_number(comment_id, current_page, i)

                # Skip comments that are before our starting point (for efficiency)
                if comment_number and comment_number < start_comment_number:
                    self.logger.debug(f"[{worker_id}] Skipping comment #{comment_number} (before start #{start_comment_number})")
                    continue

                comment_text_element = comment_element.ele('.Message')
                if not comment_text_element:
                    continue

                comment_text = comment_text_element.text.strip()
                if len(comment_text) < 10:  # Skip very short comments
                    continue

                # Check for flash sale
                detection_result = self.detector.is_flash_sale(comment_text)

                if detection_result["is_flash_sale"]:
                    flash_sale_info = {
                        "post_title": post_title,
                        "post_url": post_url,
                        "comment_id": comment_id,
                        "comment_number": comment_number,
                        "comment_text": comment_text,
                        "confidence": detection_result["confidence"],
                        "detection_reasons": detection_result["reasons"],
                        "crawled_time": datetime.now(timezone.utc).isoformat(),
                        "worker_id": worker_id,
                        "page_number": current_page,
                        "processing_strategy": "last_page_first"
                    }

                    flash_sales_found.append(flash_sale_info)

                    self.logger.info(f"[{worker_id}] FLASH SALE detected on page {current_page}! "
                                   f"Comment #{comment_number}, Confidence: {detection_result['confidence']:.2f}, "
                                   f"Comment ID: {comment_id}")
                    self.logger.debug(f"[{worker_id}] Content: {comment_text[:100]}...")

                new_processed_ids.add(comment_id)

            except Exception as e:
                self.logger.error(f"[{worker_id}] Error processing comment on page {current_page}: {e}")
                continue

        return flash_sales_found

    def _process_comments_traditional(self, worker_id: str, worker_page: ChromiumPage,
                                    post_url: str, post_title: str, current_comment_count: int,
                                    prev_processed_ids: Set[str], start_comment_number: int = 1) -> List[Dict[str, Any]]:
        """Process comments using traditional page-by-page approach (fallback method)"""
        flash_sales_found = []
        new_processed_ids = set()

        # Calculate page range
        start_page = self._calculate_page_for_comment(start_comment_number)
        max_page = self._calculate_page_for_comment(current_comment_count)

        self.logger.info(f"[{worker_id}] Traditional processing: pages {start_page} to {max_page}")

        # Safety check to prevent processing too many pages
        if max_page - start_page > 20:
            self.logger.warning(f"[{worker_id}] Large page range detected ({start_page}-{max_page}), "
                              f"limiting to 20 pages to prevent overload")
            max_page = start_page + 20

        current_page = start_page
        while current_page <= max_page and not shutdown_event.is_set():
            try:
                # Build paginated URL
                paginated_url = self._build_paginated_url(post_url, current_page)

                self.logger.info(f"[{worker_id}] Processing page {current_page}/{max_page}: {paginated_url}")

                # Navigate to the specific page
                worker_page.get(paginated_url)
                worker_page.wait.load_start()

                # Apply anti-detection delay
                time.sleep(random.uniform(1, 3))

                # Get comments on this page
                comments = worker_page.eles('.Comment')
                self.logger.info(f"[{worker_id}] Found {len(comments)} comments on page {current_page}")

                if not comments:
                    self.logger.warning(f"[{worker_id}] No comments found on page {current_page}, continuing")
                    current_page += 1
                    continue

                # Process comments on this page
                page_flash_sales = self._process_comments_on_page(
                    worker_id, comments, prev_processed_ids, new_processed_ids,
                    post_title, post_url, start_comment_number, current_page
                )
                flash_sales_found.extend(page_flash_sales)

                current_page += 1

            except Exception as e:
                self.logger.error(f"[{worker_id}] Error processing page {current_page}: {e}")
                current_page += 1
                continue

        self.logger.info(f"[{worker_id}] Traditional processing completed: "
                       f"processed {len(new_processed_ids)} new comments, "
                       f"found {len(flash_sales_found)} flash sales")

        return flash_sales_found

    def _process_comments_on_page_incremental(self, worker_id: str, comments: List, prev_processed_ids: Set[str],
                                            new_processed_ids: Set[str], post_title: str, post_url: str,
                                            start_comment_number: int, current_page: int) -> List[Dict[str, Any]]:
        """Process comments on a page with incremental filtering for new comments only"""
        flash_sales_found = []

        for i, comment_element in enumerate(comments):
            try:
                comment_id = comment_element.attr('id')
                if not comment_id:
                    continue

                # Skip if already processed
                if comment_id in prev_processed_ids or comment_id in new_processed_ids:
                    continue

                # Extract comment number from ID if possible
                comment_number = self._extract_comment_number(comment_id, current_page, i)

                # For incremental processing, only process comments >= start_comment_number
                if comment_number and comment_number < start_comment_number:
                    self.logger.debug(f"[{worker_id}] Skipping comment #{comment_number} "
                                    f"(before start #{start_comment_number})")
                    continue

                comment_text_element = comment_element.ele('.Message')
                if not comment_text_element:
                    continue

                comment_text = comment_text_element.text.strip()
                if len(comment_text) < 10:  # Skip very short comments
                    continue

                # Check for flash sale
                detection_result = self.detector.is_flash_sale(comment_text)

                if detection_result["is_flash_sale"]:
                    flash_sale_info = {
                        "post_title": post_title,
                        "post_url": post_url,
                        "comment_id": comment_id,
                        "comment_number": comment_number,
                        "comment_text": comment_text,
                        "confidence": detection_result["confidence"],
                        "detection_reasons": detection_result["reasons"],
                        "crawled_time": datetime.now(timezone.utc).isoformat(),
                        "worker_id": worker_id,
                        "page_number": current_page,
                        "processing_strategy": "last_page_first_incremental"
                    }

                    flash_sales_found.append(flash_sale_info)

                    self.logger.info(f"[{worker_id}] FLASH SALE detected on page {current_page}! "
                                   f"Comment #{comment_number}, Confidence: {detection_result['confidence']:.2f}, "
                                   f"Comment ID: {comment_id}")
                    self.logger.debug(f"[{worker_id}] Content: {comment_text[:100]}...")

                new_processed_ids.add(comment_id)

            except Exception as e:
                self.logger.error(f"[{worker_id}] Error processing comment on page {current_page}: {e}")
                continue

        return flash_sales_found

    def _extract_comment_number(self, comment_id: str, page_number: int, index_on_page: int) -> Optional[int]:
        """Extract or calculate comment number from comment ID and page info"""
        try:
            # Try to extract number from comment ID (e.g., "Comment_123" -> 123)
            if 'Comment_' in comment_id:
                return int(comment_id.split('Comment_')[1])
            elif comment_id.startswith('comment-'):
                return int(comment_id.split('comment-')[1])
            else:
                # Calculate based on page number and position
                # LowEndTalk: 30 comments per page, so page 1 = comments 1-30, page 2 = 31-60, etc.
                return ((page_number - 1) * 30) + index_on_page + 1
        except (ValueError, IndexError):
            # Fallback calculation
            return ((page_number - 1) * 30) + index_on_page + 1

    def _scroll_to_load_comments(self, page: ChromiumPage, worker_id: str) -> None:
        """Scroll page to load all comments"""
        try:
            last_height = page.scroll.to_bottom()
            scroll_attempts = 0
            max_scroll_attempts = 10

            while scroll_attempts < max_scroll_attempts:
                time.sleep(self.config.scroll_delay)
                new_height = page.scroll.to_bottom()

                if new_height == last_height:
                    break  # No more content to load

                last_height = new_height
                scroll_attempts += 1

                self.logger.debug(f"[{worker_id}] Scrolled to load more comments (attempt {scroll_attempts})")

            if scroll_attempts >= max_scroll_attempts:
                self.logger.warning(f"[{worker_id}] Reached maximum scroll attempts")

        except Exception as e:
            self.logger.error(f"[{worker_id}] Error during scrolling: {e}")

    def _update_processed_post_state(self, post_url: str, comment_count: int,
                                   datetime_attr: str, processed_ids: Set[str]) -> None:
        """Update the processed state for a post"""
        try:
            current_state = self.state_manager.load_state()
            processed_posts = current_state.get("processed_posts", {})

            processed_posts[post_url] = {
                "last_comment_count": comment_count,
                "last_comment_datetime": datetime_attr,
                "processed_comment_ids": list(processed_ids)
            }

            current_state["processed_posts"] = processed_posts
            self.state_manager.save_state(current_state)

        except Exception as e:
            self.logger.error(f"Error updating post state: {e}")


def main():
    """Main function to run the improved forum crawler"""
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "forum_crawler.log")

    try:
        # Load configuration
        config = CrawlerConfig.from_file("crawler_config.json")

        logger.info("Starting Improved Forum Crawler")
        logger.info(f"Configuration: {config.num_workers} workers, "
                   f"{config.refresh_interval}s refresh interval")

        # Create and start crawler
        crawler = ForumCrawler(config)
        crawler.start()

    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt, shutting down...")
    except Exception as e:
        logger.error(f"Critical error in main: {e}")
    finally:
        logger.info("Forum crawler shutdown complete")


if __name__ == "__main__":
    main()
