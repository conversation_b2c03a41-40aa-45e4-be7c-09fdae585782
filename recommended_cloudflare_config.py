#!/usr/bin/env python3
"""
推荐的 Cloudflare 绕过配置
基于测试结果的最佳实践配置
"""

import time
import random
import sys
from DrissionPage import ChromiumOptions, ChromiumPage

# 🎯 推荐的配置
RECOMMENDED_CONFIG = {
    # 最佳 User-Agent（基于测试结果）
    "user_agents": [
        # Chrome 117 - 测试中表现最好
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
        # Chrome 116 - 备选
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
        # Chrome 115 - 稳定版本
        "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
    ],
    
    # 浏览器参数
    "browser_args": [
        "--no-sandbox",
        "--disable-blink-features=AutomationControlled",
        "--window-size=1366,768",
        "--lang=zh-CN,zh;q=0.9,en;q=0.8",
        "--user-data-dir=./chrome_profile_nodeseek"
    ],
    
    # 时间配置
    "timing": {
        "initial_wait": (3, 7),      # 初始等待时间
        "iframe_wait": (2, 4),       # iframe 加载等待
        "verification_wait": (10, 18), # 验证等待时间
        "retry_wait": (3, 6),        # 重试间隔
        "scroll_delay": (0.5, 1.5)   # 滚动延迟
    },
    
    # 重试配置
    "retries": {
        "max_attempts": 8,           # 最大重试次数
        "timeout_per_attempt": 30    # 每次尝试超时
    }
}


def create_optimal_browser_options(user_agent: str = None) -> ChromiumOptions:
    """创建最优的浏览器选项"""
    options = ChromiumOptions()
    
    # 关键：使用有头模式
    options.headless(False)
    
    # 设置用户代理
    if not user_agent:
        user_agent = RECOMMENDED_CONFIG["user_agents"][0]
    options.set_user_agent(user_agent)
    
    # 应用推荐的浏览器参数
    for arg in RECOMMENDED_CONFIG["browser_args"]:
        options.set_argument(arg)
    
    return options


def enhanced_cloudflare_handler(tab: ChromiumPage, pid: str = "unknown") -> bool:
    """
    增强的 Cloudflare 处理器
    基于你的原始代码优化
    """
    config = RECOMMENDED_CONFIG
    max_retries = config["retries"]["max_attempts"]
    retries = 0
    
    print(f"🛡️ 开始 Cloudflare 处理 (PID: {pid})")
    
    while retries < max_retries:
        try:
            print(f"🔄 Cloudflare 处理尝试 {retries + 1}/{max_retries}")
            
            # 获取当前页面状态
            current_title = tab.title
            print(f"📄 当前页面标题: {current_title}")
            
            if 'Just a moment' in current_title or '请稍候' in current_title:
                print("🔍 检测到 Cloudflare 挑战页面")
                
                # 模拟人类等待
                wait_time = random.uniform(*config["timing"]["initial_wait"])
                print(f"⏳ 模拟人类等待 {wait_time:.1f} 秒...")
                time.sleep(wait_time)
                
                # 模拟人类滚动行为
                try:
                    scroll_amount = random.randint(100, 300)
                    tab.scroll.down(scroll_amount)
                    time.sleep(random.uniform(*config["timing"]["scroll_delay"]))
                    tab.scroll.up(scroll_amount // 2)
                    time.sleep(random.uniform(*config["timing"]["scroll_delay"]))
                except Exception as e:
                    print(f"⚠️ 滚动失败: {e}")
                
                # 查找主要内容区域
                if tab.s_ele('x://div[@class="main-content"]'):
                    print("✅ 找到 main-content 区域")
                    mainDiv = tab.ele('x://div[@class="main-content"]/div')
                    
                    # 检查并点击输入框
                    if mainDiv.ele('x:/div/input', timeout=0.5):
                        print("🖱️ 找到输入框，点击")
                        input_elem = tab.s_ele('x:/div/input')
                        if input_elem:
                            time.sleep(random.uniform(0.5, 1.5))
                            input_elem.click()
                            time.sleep(random.uniform(1, 2))
                    
                    # 查找 iframe
                    iframe = mainDiv.ele('x:/div/div').sr('x://iframe')
                    if iframe:
                        print("🖼️ 找到 iframe")
                        
                        # 等待 iframe 加载
                        iframe_wait = random.uniform(*config["timing"]["iframe_wait"])
                        print(f"⏳ 等待 iframe 加载 {iframe_wait:.1f} 秒...")
                        time.sleep(iframe_wait)
                        
                        body = iframe.ele('x://body')
                        if body:
                            print("✅ 找到 iframe body")
                            
                            checkbox = body.sr('x://input[@type="checkbox"]')
                            if checkbox:
                                print("☑️ 找到复选框，模拟人类点击")
                                
                                # 模拟鼠标移动到复选框
                                time.sleep(random.uniform(1, 2))
                                
                                # 点击复选框
                                checkbox.click()
                                
                                # 等待验证处理
                                verification_wait = random.uniform(*config["timing"]["verification_wait"])
                                print(f"⏳ 等待验证处理 {verification_wait:.1f} 秒...")
                                time.sleep(verification_wait)
                                
                                # 检查验证结果
                                new_title = tab.title
                                print(f"📄 验证后页面标题: {new_title}")
                                
                                if 'Just a moment' not in new_title and '请稍候' not in new_title:
                                    print("🎉 Cloudflare 挑战成功通过！")
                                    return True
                                else:
                                    print("⚠️ 验证后仍显示挑战页面，继续尝试...")
                                    retries += 1
                                    continue
                            else:
                                print("❌ 未找到复选框")
                        else:
                            print("❌ 未找到 iframe body")
                    else:
                        print("❌ 未找到 iframe")
                else:
                    print("❌ 未找到 main-content，尝试等待自动解决...")
                    
                    # 尝试等待页面自动解决
                    auto_wait = 10
                    print(f"⏳ 等待自动解决 {auto_wait} 秒...")
                    time.sleep(auto_wait)
                    
                    auto_title = tab.title
                    if 'Just a moment' not in auto_title and '请稍候' not in auto_title:
                        print("🎉 页面自动解决了挑战！")
                        return True
            else:
                print("✅ 没有 Cloudflare 挑战，直接通过")
                return True
                
        except Exception as e:
            exc_type, exc_value, exc_tb = sys.exc_info()
            line_number = exc_tb.tb_lineno
            print(f"❌ Cloudflare 处理异常 at line {line_number}: {e}")
        
        # 准备重试
        retries += 1
        if retries < max_retries:
            retry_wait = random.uniform(*config["timing"]["retry_wait"])
            print(f"⏳ 等待 {retry_wait:.1f} 秒后重试...")
            time.sleep(retry_wait)
    
    print("❌ Cloudflare 挑战处理失败")
    return False


def test_recommended_config():
    """测试推荐配置"""
    print("🧪 测试推荐的 Cloudflare 配置")
    print("=" * 50)
    
    # 使用推荐配置创建页面
    options = create_optimal_browser_options()
    page = ChromiumPage(addr_or_opts=options)
    
    try:
        # 导航到目标页面
        url = "https://www.nodeseek.com/"
        print(f"🌐 导航到 {url}")
        page.get(url)
        
        # 等待初始加载
        time.sleep(5)
        
        # 使用增强的处理器
        success = enhanced_cloudflare_handler(page, "test")
        
        if success:
            print("🎉 SUCCESS! 推荐配置有效！")
            
            # 验证页面内容
            final_title = page.title
            print(f"📄 最终页面标题: {final_title}")
            
            # 保持浏览器打开供观察
            print("🔍 保持浏览器打开 30 秒供观察...")
            time.sleep(30)
        else:
            print("❌ 推荐配置未能绕过 Cloudflare")
        
        page.close()
        return success
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


# 🎯 最终推荐总结
FINAL_RECOMMENDATIONS = """
🎯 基于测试的最终推荐：

1. 📱 User-Agent:
   推荐使用: Chrome 117.0.0.0
   "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36"

2. 🔧 浏览器配置:
   - 必须使用有头模式 (headless=False)
   - 最小化参数，只保留必要的反检测设置
   - 使用独立的用户数据目录

3. ⏱️ 时间策略:
   - 初始等待: 3-7 秒
   - iframe 等待: 2-4 秒  
   - 验证等待: 10-18 秒
   - 重试间隔: 3-6 秒

4. 🔄 重试策略:
   - 最大重试次数: 8 次
   - 每次尝试都模拟人类行为
   - 包含滚动和随机延迟

5. 🎭 人类行为模拟:
   - 随机等待时间
   - 页面滚动
   - 鼠标移动模拟
   - 自然的点击间隔

你的 cloudflare 方法逻辑是正确的，主要需要：
- 使用推荐的 User-Agent
- 增加等待时间
- 添加更多人类行为模拟
- 增加重试次数
"""


if __name__ == "__main__":
    print(FINAL_RECOMMENDATIONS)
    print("\n" + "=" * 60)
    
    # 运行测试
    test_recommended_config()
