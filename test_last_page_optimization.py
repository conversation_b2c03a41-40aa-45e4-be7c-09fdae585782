#!/usr/bin/env python3
"""
Test script for the last-page navigation optimization
"""

import sys
import os

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import ForumCraw<PERSON>, CrawlerConfig


def test_last_page_calculations():
    """Test the last page calculation methods"""
    print("Testing last page calculation methods...")
    
    # Create a minimal config and crawler instance
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    # Test _calculate_last_page
    test_cases = [
        (25, 1),    # 25 comments -> 1 page
        (30, 1),    # 30 comments -> 1 page (exactly 30 per page)
        (31, 2),    # 31 comments -> 2 pages
        (60, 2),    # 60 comments -> 2 pages
        (61, 3),    # 61 comments -> 3 pages
        (90, 3),    # 90 comments -> 3 pages
        (91, 4),    # 91 comments -> 4 pages
        (167, 6),   # 167 comments -> 6 pages (from your example)
        (200, 7),   # 200 comments -> 7 pages
    ]
    
    print("\nTesting _calculate_last_page:")
    for comment_count, expected_page in test_cases:
        actual_page = crawler._calculate_last_page(comment_count)
        status = "✓" if actual_page == expected_page else "✗"
        print(f"  {status} {comment_count:3d} comments -> Page {actual_page} (expected {expected_page})")
    
    # Test _build_last_page_url
    base_url = "https://lowendtalk.com/discussion/207564/hostdare-vps-offers-usa-japan-bulgaria-daily-giveaways"
    
    print("\nTesting _build_last_page_url:")
    url_test_cases = [
        (25, base_url, 1),                    # 25 comments -> page 1, no suffix
        (40, f"{base_url}/p2", 2),           # 40 comments -> page 2, /p2 suffix
        (90, f"{base_url}/p3", 3),           # 90 comments -> page 3, /p3 suffix
        (167, f"{base_url}/p6", 6),          # 167 comments -> page 6, /p6 suffix
        (200, f"{base_url}/p7", 7),          # 200 comments -> page 7, /p7 suffix
    ]
    
    for comment_count, expected_url, expected_page in url_test_cases:
        actual_url, actual_page = crawler._build_last_page_url(base_url, comment_count)
        url_status = "✓" if actual_url == expected_url else "✗"
        page_status = "✓" if actual_page == expected_page else "✗"
        print(f"  {url_status}{page_status} {comment_count:3d} comments -> {actual_url} (page {actual_page})")
        if actual_url != expected_url:
            print(f"      Expected URL: {expected_url}")
        if actual_page != expected_page:
            print(f"      Expected page: {expected_page}")


def test_optimization_scenarios():
    """Test real-world optimization scenarios"""
    print("\n\nTesting optimization scenarios...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    scenarios = [
        {
            "name": "Small post (25 comments)",
            "comment_count": 25,
            "description": "Should navigate to base URL (no pagination)"
        },
        {
            "name": "Medium post (40 comments)", 
            "comment_count": 40,
            "description": "Should navigate to /p2 (page 2)"
        },
        {
            "name": "Large post (167 comments)",
            "comment_count": 167, 
            "description": "Should navigate to /p6 (page 6)"
        },
        {
            "name": "Very large post (500 comments)",
            "comment_count": 500,
            "description": "Should navigate to /p17 (page 17)"
        }
    ]
    
    base_url = "https://lowendtalk.com/discussion/test"
    
    for scenario in scenarios:
        print(f"\nScenario: {scenario['name']}")
        print(f"  Description: {scenario['description']}")
        
        comment_count = scenario['comment_count']
        last_page_url, last_page = crawler._build_last_page_url(base_url, comment_count)
        
        print(f"  Comment count: {comment_count}")
        print(f"  Last page: {last_page}")
        print(f"  URL: {last_page_url}")
        
        # Verify the calculation
        expected_page = (comment_count + 29) // 30  # Ceiling division
        if last_page == expected_page:
            print(f"  ✓ Calculation correct")
        else:
            print(f"  ✗ Calculation incorrect (expected page {expected_page})")


def test_performance_comparison():
    """Compare performance benefits of last-page-first approach"""
    print("\n\nPerformance comparison analysis...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    test_posts = [
        {"comments": 50, "name": "Small post"},
        {"comments": 150, "name": "Medium post"}, 
        {"comments": 300, "name": "Large post"},
        {"comments": 600, "name": "Very large post"},
    ]
    
    print("\nTraditional approach vs Last-page-first approach:")
    print("=" * 70)
    print(f"{'Post Type':<15} {'Comments':<10} {'Traditional':<15} {'Last-Page':<15} {'Savings':<10}")
    print("-" * 70)
    
    for post in test_posts:
        comment_count = post['comments']
        name = post['name']
        
        # Traditional approach: start from page 1
        traditional_pages = crawler._calculate_last_page(comment_count)
        
        # Last-page-first approach: navigate directly to last page
        last_page_pages = 1  # Only need to load the last page
        
        savings = traditional_pages - last_page_pages
        savings_pct = (savings / traditional_pages) * 100 if traditional_pages > 0 else 0
        
        print(f"{name:<15} {comment_count:<10} {traditional_pages:<15} {last_page_pages:<15} {savings} ({savings_pct:.0f}%)")
    
    print("\nKey benefits of last-page-first approach:")
    print("• Immediate access to newest comments (most likely to contain flash sales)")
    print("• Reduced page loads (1 page vs multiple pages)")
    print("• Faster processing time")
    print("• Lower bandwidth usage")
    print("• Better user experience with quicker results")


if __name__ == "__main__":
    print("Last-Page Navigation Optimization Test")
    print("=" * 50)
    
    try:
        test_last_page_calculations()
        test_optimization_scenarios()
        test_performance_comparison()
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print("Last-page optimization tests completed!")
