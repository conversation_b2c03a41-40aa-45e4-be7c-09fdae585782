#!/usr/bin/env python3
"""
Test script for the pagination logic fixes in the forum crawler
"""

import sys
import os

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import ForumCrawler, <PERSON>rawlerConfig


def test_pagination_calculations():
    """Test the pagination calculation methods"""
    print("Testing pagination calculation methods...")
    
    # Create a minimal config and crawler instance
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    # Test _calculate_page_for_comment
    test_cases = [
        (1, 1),      # Comment 1 -> Page 1
        (30, 1),     # Comment 30 -> Page 1 (last comment on page 1)
        (31, 2),     # Comment 31 -> Page 2 (first comment on page 2)
        (60, 2),     # Comment 60 -> Page 2 (last comment on page 2)
        (61, 3),     # Comment 61 -> Page 3
        (167, 6),    # Comment 167 -> Page 6 (from your example)
        (168, 6),    # Comment 168 -> Page 6 (the new comment to process)
        (180, 6),    # Comment 180 -> Page 6 (last comment on page 6)
        (181, 7),    # Comment 181 -> Page 7
    ]
    
    print("\nTesting _calculate_page_for_comment:")
    for comment_num, expected_page in test_cases:
        actual_page = crawler._calculate_page_for_comment(comment_num)
        status = "✓" if actual_page == expected_page else "✗"
        print(f"  {status} Comment {comment_num:3d} -> Page {actual_page} (expected {expected_page})")
    
    # Test _build_paginated_url
    base_url = "https://lowendtalk.com/discussion/207564/hostdare-vps-offers-usa-japan-bulgaria-daily-giveaways"
    
    print("\nTesting _build_paginated_url:")
    url_test_cases = [
        (1, base_url),  # Page 1 -> no suffix
        (2, f"{base_url}/p2"),  # Page 2 -> /p2
        (3, f"{base_url}/p3"),  # Page 3 -> /p3
        (6, f"{base_url}/p6"),  # Page 6 -> /p6
    ]
    
    for page_num, expected_url in url_test_cases:
        actual_url = crawler._build_paginated_url(base_url, page_num)
        status = "✓" if actual_url == expected_url else "✗"
        print(f"  {status} Page {page_num} -> {actual_url}")
        if actual_url != expected_url:
            print(f"      Expected: {expected_url}")
    
    # Test with URL that already has pagination
    print("\nTesting _build_paginated_url with existing pagination:")
    existing_paginated_url = f"{base_url}/p3"
    new_url = crawler._build_paginated_url(existing_paginated_url, 5)
    expected_new_url = f"{base_url}/p5"
    status = "✓" if new_url == expected_new_url else "✗"
    print(f"  {status} {existing_paginated_url} -> Page 5 -> {new_url}")
    if new_url != expected_new_url:
        print(f"      Expected: {expected_new_url}")


def test_comment_number_extraction():
    """Test the comment number extraction logic"""
    print("\n\nTesting comment number extraction...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    test_cases = [
        # (comment_id, page_number, index_on_page, expected_number)
        ("Comment_168", 6, 7, 168),  # Direct extraction from ID
        ("comment-175", 6, 14, 175),  # Alternative ID format
        ("some_other_id", 1, 0, 1),   # Fallback calculation: page 1, index 0 -> comment 1
        ("some_other_id", 1, 29, 30), # Fallback calculation: page 1, index 29 -> comment 30
        ("some_other_id", 2, 0, 31),  # Fallback calculation: page 2, index 0 -> comment 31
        ("some_other_id", 6, 7, 158), # Fallback calculation: page 6, index 7 -> comment 158
    ]
    
    print("Testing _extract_comment_number:")
    for comment_id, page_num, index, expected_num in test_cases:
        actual_num = crawler._extract_comment_number(comment_id, page_num, index)
        status = "✓" if actual_num == expected_num else "✗"
        print(f"  {status} ID='{comment_id}', Page={page_num}, Index={index} -> Comment #{actual_num} (expected #{expected_num})")


def test_real_world_scenario():
    """Test the real-world scenario from the user's issue"""
    print("\n\nTesting real-world scenario...")
    print("Scenario: JSON shows 167 comments processed, need to find comment #168")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    prev_comment_count = 167
    current_comment_count = 200  # Assume there are now 200 comments
    start_comment_number = prev_comment_count + 1  # 168
    
    start_page = crawler._calculate_page_for_comment(start_comment_number)
    max_page = crawler._calculate_page_for_comment(current_comment_count)
    
    base_url = "https://lowendtalk.com/discussion/207564/hostdare-vps-offers-usa-japan-bulgaria-daily-giveaways"
    start_url = crawler._build_paginated_url(base_url, start_page)
    
    print(f"Previous comment count: {prev_comment_count}")
    print(f"Current comment count: {current_comment_count}")
    print(f"First new comment to process: #{start_comment_number}")
    print(f"Starting page: {start_page}")
    print(f"Maximum page: {max_page}")
    print(f"Starting URL: {start_url}")
    print(f"Pages to process: {start_page} to {max_page}")
    
    # Verify this matches the expected behavior
    expected_start_page = 6  # ceil(168/30) = 6
    expected_start_url = f"{base_url}/p6"
    
    if start_page == expected_start_page and start_url == expected_start_url:
        print("✓ Real-world scenario test PASSED")
    else:
        print("✗ Real-world scenario test FAILED")
        print(f"  Expected start page: {expected_start_page}, got: {start_page}")
        print(f"  Expected start URL: {expected_start_url}, got: {start_url}")


if __name__ == "__main__":
    print("Forum Crawler Pagination Logic Test")
    print("=" * 50)
    
    test_pagination_calculations()
    test_comment_number_extraction()
    test_real_world_scenario()
    
    print("\n" + "=" * 50)
    print("Test completed!")
