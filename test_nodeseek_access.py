#!/usr/bin/env python3
"""
Simple test for NodeSeek access with Cloudflare bypass
"""

import time
import logging
from cloudflare_bypass import CloudflareBypass

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_nodeseek_simple():
    """Simple test for NodeSeek access"""
    logger.info("🧪 Testing NodeSeek Access")
    
    try:
        # Create bypass instance
        bypass = CloudflareBypass()
        
        # Create stealth page
        logger.info("Creating stealth browser...")
        page = bypass.create_stealth_page()
        
        # Test URL
        url = "https://www.nodeseek.com/"
        
        logger.info(f"Navigating to {url}...")
        
        # Simple navigation first
        page.get(url)
        time.sleep(5)
        
        # Check what we got
        title = page.title
        logger.info(f"Page title: {title}")
        
        # Check for Cloudflare
        if bypass._is_cloudflare_challenge(page):
            logger.info("Cloudflare challenge detected")
            
            # Wait longer for automatic resolution
            logger.info("Waiting for challenge to resolve automatically...")
            time.sleep(15)
            
            # Check again
            if not bypass._is_cloudflare_challenge(page):
                logger.info("✅ Challenge resolved automatically")
            else:
                logger.info("Challenge still present, trying manual handling...")
                success = bypass._handle_cloudflare_challenge(page, timeout=45)
                if success:
                    logger.info("✅ Challenge handled successfully")
                else:
                    logger.warning("❌ Challenge handling failed")
        else:
            logger.info("✅ No Cloudflare challenge detected")
        
        # Final check
        final_title = page.title
        page_text = page.html[:500]
        
        logger.info(f"Final page title: {final_title}")
        logger.info(f"Page content preview: {page_text}...")
        
        # Check for success indicators
        success_indicators = ['nodeseek', 'forum', 'post', 'user', 'login']
        page_lower = page.html.lower()
        
        found_indicators = [ind for ind in success_indicators if ind in page_lower]
        
        if found_indicators:
            logger.info(f"✅ Success indicators found: {found_indicators}")
            logger.info("✅ NodeSeek access successful!")
        else:
            logger.warning("⚠️  No clear success indicators found")
        
        # Keep browser open for manual inspection
        logger.info("Browser will stay open for 30 seconds for manual inspection...")
        time.sleep(30)
        
        page.close()
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")


def test_nodeseek_with_task_manager():
    """Test NodeSeek access using the task manager approach"""
    logger.info("🧪 Testing NodeSeek with Task Manager Approach")
    
    try:
        from task_browser_manager import TaskBrowserManager
        from improved_forum_crawler import CrawlerConfig
        
        # Create config
        config = CrawlerConfig()
        config.headless = False  # Use headed mode for Cloudflare
        
        # Create browser manager
        browser_manager = TaskBrowserManager(config)
        
        # Create task tab
        task_id = "test_nodeseek"
        post_url = "https://www.nodeseek.com/"
        domain = "nodeseek.com"
        
        logger.info("Creating task tab...")
        page = browser_manager.create_task_tab(task_id, post_url, domain)
        
        if page:
            logger.info("✅ Task tab created successfully")
            
            # Check page content
            title = page.title
            logger.info(f"Page title: {title}")
            
            # Wait and check
            time.sleep(10)
            
            # Final check
            page_text = page.html[:500]
            logger.info(f"Page content: {page_text}...")
            
            # Keep open for inspection
            logger.info("Keeping browser open for 20 seconds...")
            time.sleep(20)
            
            # Close tab
            browser_manager.close_task_tab(task_id)
            logger.info("✅ Test completed")
        else:
            logger.error("❌ Failed to create task tab")
        
        # Cleanup
        browser_manager.cleanup_all()
        
    except Exception as e:
        logger.error(f"❌ Task manager test failed: {e}")


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test NodeSeek Access')
    parser.add_argument('--method', choices=['simple', 'task'], default='simple',
                       help='Test method to use')
    
    args = parser.parse_args()
    
    print("🌐 NodeSeek Access Test")
    print("=" * 30)
    
    if args.method == 'simple':
        test_nodeseek_simple()
    elif args.method == 'task':
        test_nodeseek_with_task_manager()


if __name__ == "__main__":
    main()
