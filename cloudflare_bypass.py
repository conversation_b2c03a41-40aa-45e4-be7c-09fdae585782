"""
Enhanced Cloudflare Bypass Module
Advanced anti-detection techniques for bypassing Cloudflare protection
"""

import time
import random
import logging
from typing import Optional, Dict, List
from DrissionPage import ChromiumPage, ChromiumOptions
from DrissionPage.common import Settings


class CloudflareBypass:
    """Advanced Cloudflare bypass with multiple anti-detection techniques"""
    
    def __init__(self):
        self.logger = logging.getLogger("cloudflare_bypass")
        
        # Enhanced user agents (real browser fingerprints)
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0",
            "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
        ]
        
        # Real browser viewport sizes
        self.viewports = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900),
            (1280, 720), (1600, 900), (2560, 1440), (1920, 1200)
        ]
    
    def create_stealth_options(self) -> ChromiumOptions:
        """Create ChromiumOptions with advanced stealth settings"""
        options = ChromiumOptions()

        # Basic stealth settings - 使用有头模式，更难检测
        options.headless(False)
        
        # 核心反检测参数
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-blink-features=AutomationControlled')
        options.set_argument('--disable-dev-shm-usage')
        options.set_argument('--disable-extensions')
        options.set_argument('--disable-plugins')
        options.set_argument('--disable-default-apps')
        options.set_argument('--disable-background-timer-throttling')
        options.set_argument('--disable-backgrounding-occluded-windows')
        options.set_argument('--disable-renderer-backgrounding')
        options.set_argument('--disable-features=TranslateUI')
        options.set_argument('--disable-ipc-flooding-protection')
        
        # 内存和性能优化
        options.set_argument('--memory-pressure-off')
        options.set_argument('--max_old_space_size=4096')
        
        # 网络相关
        options.set_argument('--aggressive-cache-discard')
        options.set_argument('--disable-background-networking')
        
        # 随机化设置
        viewport = random.choice(self.viewports)
        options.set_argument(f'--window-size={viewport[0]},{viewport[1]}')
        
        # 随机用户代理
        user_agent = random.choice(self.user_agents)
        options.set_user_agent(user_agent)
        
        # 语言设置
        options.set_argument('--lang=zh-CN,zh;q=0.9,en;q=0.8')
        
        # 禁用自动化检测
        options.set_argument('--exclude-switches=enable-automation')
        options.set_argument('--disable-blink-features=AutomationControlled')
        
        return options
    
    def create_stealth_page(self) -> ChromiumPage:
        """Create a stealth ChromiumPage with anti-detection"""
        options = self.create_stealth_options()
        
        try:
            # 创建页面
            page = ChromiumPage(addr_or_opts=options)
            
            # 执行反检测脚本
            self._inject_stealth_scripts(page)
            
            return page
            
        except Exception as e:
            self.logger.error(f"Failed to create stealth page: {e}")
            raise
    
    def _inject_stealth_scripts(self, page: ChromiumPage):
        """注入反检测脚本"""
        try:
            # 隐藏 webdriver 属性
            stealth_script = """
            // 隐藏 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 修改 plugins 长度
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });
            
            // 修改 languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en'],
            });
            
            // 隐藏自动化相关属性
            window.chrome = {
                runtime: {},
            };
            
            // 修改 permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 伪造 canvas 指纹
            const getContext = HTMLCanvasElement.prototype.getContext;
            HTMLCanvasElement.prototype.getContext = function(type) {
                if (type === '2d') {
                    const context = getContext.call(this, type);
                    const originalFillText = context.fillText;
                    context.fillText = function(text, x, y, maxWidth) {
                        // 添加微小的随机偏移
                        const offset = Math.random() * 0.1;
                        return originalFillText.call(this, text, x + offset, y + offset, maxWidth);
                    };
                    return context;
                }
                return getContext.call(this, type);
            };
            """
            
            page.run_js(stealth_script)
            self.logger.debug("Stealth scripts injected successfully")
            
        except Exception as e:
            self.logger.warning(f"Failed to inject stealth scripts: {e}")
    
    def navigate_with_retry(self, page: ChromiumPage, url: str, max_retries: int = 3) -> bool:
        """带重试的导航，处理 Cloudflare 挑战"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")
                
                # 随机延迟
                time.sleep(random.uniform(2, 5))
                
                # 导航到页面
                page.get(url)
                
                # 等待页面加载
                time.sleep(random.uniform(3, 6))
                
                # 检查是否遇到 Cloudflare 挑战
                if self._is_cloudflare_challenge(page):
                    self.logger.info("Cloudflare challenge detected, waiting...")
                    if self._handle_cloudflare_challenge(page):
                        self.logger.info("Cloudflare challenge passed")
                        return True
                    else:
                        self.logger.warning(f"Cloudflare challenge failed on attempt {attempt + 1}")
                        continue
                else:
                    self.logger.info("Page loaded successfully without challenge")
                    return True
                    
            except Exception as e:
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {e}")
                time.sleep(random.uniform(5, 10))
        
        return False
    
    def _is_cloudflare_challenge(self, page: ChromiumPage) -> bool:
        """检测是否遇到 Cloudflare 挑战"""
        try:
            # 检查常见的 Cloudflare 挑战标识
            cloudflare_indicators = [
                'Checking your browser before accessing',
                'DDoS protection by Cloudflare',
                'cf-browser-verification',
                'cf-challenge-form',
                'Cloudflare',
                'Just a moment',
                'Please wait while we check your browser'
            ]
            
            page_text = page.html.lower()
            title = page.title.lower()
            
            for indicator in cloudflare_indicators:
                if indicator.lower() in page_text or indicator.lower() in title:
                    return True
            
            # 检查特定元素
            cf_elements = [
                'css:#cf-challenge-form',
                'css:.cf-browser-verification',
                'css:[data-ray]'
            ]
            
            for selector in cf_elements:
                if page.ele(selector, timeout=1):
                    return True
            
            return False
            
        except Exception as e:
            self.logger.warning(f"Error checking Cloudflare challenge: {e}")
            return False
    
    def _handle_cloudflare_challenge(self, page: ChromiumPage, timeout: int = 60) -> bool:
        """处理 Cloudflare 挑战"""
        try:
            self.logger.info("Handling Cloudflare challenge...")

            # 等待初始加载
            time.sleep(random.uniform(3, 5))

            # 模拟人类行为
            self._simulate_human_behavior(page)

            # 等待挑战完成
            start_time = time.time()
            check_interval = 2

            while time.time() - start_time < timeout:
                # 检查是否还在挑战页面
                if not self._is_cloudflare_challenge(page):
                    self.logger.info("Cloudflare challenge completed")
                    # 额外等待确保页面完全加载
                    time.sleep(random.uniform(2, 4))
                    return True

                # 检查是否有验证码或其他交互元素
                self._check_and_handle_captcha(page)

                # 随机移动鼠标和滚动
                self._random_mouse_movement(page)

                # 模拟更多人类行为
                if random.random() < 0.3:  # 30% 概率
                    self._simulate_human_behavior(page)

                time.sleep(check_interval)

            self.logger.warning(f"Cloudflare challenge timeout after {timeout} seconds")
            return False

        except Exception as e:
            self.logger.error(f"Error handling Cloudflare challenge: {e}")
            return False
    
    def _simulate_human_behavior(self, page: ChromiumPage):
        """模拟人类浏览行为"""
        try:
            # 随机滚动
            for _ in range(random.randint(2, 5)):
                scroll_amount = random.randint(100, 500)
                page.scroll.to_bottom(scroll_amount)
                time.sleep(random.uniform(0.5, 1.5))
            
            # 随机点击空白区域
            try:
                page.click((random.randint(100, 300), random.randint(100, 300)))
                time.sleep(random.uniform(0.5, 1))
            except:
                pass
            
        except Exception as e:
            self.logger.debug(f"Error simulating human behavior: {e}")
    
    def _random_mouse_movement(self, page: ChromiumPage):
        """随机鼠标移动"""
        try:
            # 模拟鼠标移动
            x = random.randint(100, 800)
            y = random.randint(100, 600)
            page.actions.move_to((x, y))
            time.sleep(random.uniform(0.1, 0.5))
            
        except Exception as e:
            self.logger.debug(f"Error with mouse movement: {e}")

    def _check_and_handle_captcha(self, page: ChromiumPage):
        """检查并处理验证码"""
        try:
            # 检查常见的验证码元素
            captcha_selectors = [
                'css:iframe[src*="captcha"]',
                'css:iframe[src*="recaptcha"]',
                'css:.cf-turnstile',
                'css:#cf-challenge-form',
                'css:[data-sitekey]'
            ]

            for selector in captcha_selectors:
                element = page.ele(selector, timeout=1)
                if element:
                    self.logger.info(f"Found captcha element: {selector}")
                    # 对于某些验证码，可能需要点击
                    if 'turnstile' in selector or 'challenge' in selector:
                        try:
                            element.click()
                            time.sleep(random.uniform(1, 2))
                        except:
                            pass
                    break

        except Exception as e:
            self.logger.debug(f"Error checking captcha: {e}")

    def wait_for_page_load(self, page: ChromiumPage, timeout: int = 30):
        """等待页面完全加载"""
        try:
            # 等待页面加载完成
            page.wait.load_start()
            
            # 额外等待确保内容加载
            time.sleep(random.uniform(2, 4))
            
            # 检查页面是否可交互
            start_time = time.time()
            while time.time() - start_time < timeout:
                try:
                    # 尝试执行简单的 JavaScript
                    page.run_js("return document.readyState")
                    break
                except:
                    time.sleep(1)
            
        except Exception as e:
            self.logger.warning(f"Error waiting for page load: {e}")


def create_cloudflare_resistant_page() -> ChromiumPage:
    """创建抗 Cloudflare 检测的页面"""
    bypass = CloudflareBypass()
    return bypass.create_stealth_page()


def navigate_with_cloudflare_bypass(page: ChromiumPage, url: str) -> bool:
    """使用 Cloudflare 绕过技术导航到 URL"""
    bypass = CloudflareBypass()
    return bypass.navigate_with_retry(page, url)
