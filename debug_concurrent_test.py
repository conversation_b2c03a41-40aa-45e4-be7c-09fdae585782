#!/usr/bin/env python3
"""
Debug script for concurrent access issues
"""

import sys
import os
import time
import threading
import json
from concurrent.futures import ThreadPoolExecutor

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import CrawlerConfig, StateManager


def debug_single_operation():
    """Test a single state operation"""
    print("Testing single state operation...")
    
    config = CrawlerConfig()
    config.redis_enabled = False
    config.state_file = "debug_state.json"
    
    # Clean up any existing file
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    state_manager = StateManager(config)
    
    # Test basic save and load
    test_state = {"counter": 1, "test": "data"}
    state_manager.save_state(test_state)
    
    loaded_state = state_manager.load_state()
    print(f"Saved: {test_state}")
    print(f"Loaded: {loaded_state}")
    
    # Check file contents
    if os.path.exists(config.state_file):
        with open(config.state_file, 'r') as f:
            file_contents = f.read()
        print(f"File contents: {file_contents}")
    
    # Clean up
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    return loaded_state.get("counter") == 1


def debug_sequential_operations():
    """Test sequential operations"""
    print("\nTesting sequential operations...")
    
    config = CrawlerConfig()
    config.redis_enabled = False
    config.state_file = "debug_sequential.json"
    
    # Clean up any existing file
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    state_manager = StateManager(config)
    
    # Perform multiple sequential operations
    for i in range(5):
        current_state = state_manager.load_state()
        
        if "counter" not in current_state:
            current_state["counter"] = 0
        
        current_state["counter"] += 1
        current_state[f"operation_{i}"] = f"completed_at_{time.time()}"
        
        state_manager.save_state(current_state)
        print(f"Operation {i}: counter = {current_state['counter']}")
    
    # Check final state
    final_state = state_manager.load_state()
    print(f"Final state: {final_state}")
    
    # Clean up
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    return final_state.get("counter") == 5


def debug_concurrent_simple():
    """Test simple concurrent operations with detailed logging"""
    print("\nTesting simple concurrent operations...")
    
    config = CrawlerConfig()
    config.redis_enabled = False
    config.state_file = "debug_concurrent.json"
    
    # Clean up any existing file
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    results = []
    errors = []
    
    def worker_function(worker_id):
        """Simple worker function"""
        try:
            print(f"Worker {worker_id} starting...")
            state_manager = StateManager(config)
            
            # Load current state
            current_state = state_manager.load_state()
            print(f"Worker {worker_id} loaded state: {current_state}")
            
            # Modify state
            if "counter" not in current_state:
                current_state["counter"] = 0
            
            old_counter = current_state["counter"]
            current_state["counter"] += 1
            current_state[f"worker_{worker_id}"] = f"processed_at_{time.time()}"
            
            print(f"Worker {worker_id} updating counter: {old_counter} -> {current_state['counter']}")
            
            # Save modified state
            state_manager.save_state(current_state)
            print(f"Worker {worker_id} saved state")
            
            results.append(f"Worker {worker_id} completed")
            
        except Exception as e:
            error_msg = f"Worker {worker_id} error: {e}"
            print(error_msg)
            errors.append(error_msg)
    
    # Run workers sequentially first to test
    print("Running workers sequentially...")
    for i in range(3):
        worker_function(i)
    
    # Check intermediate state
    state_manager = StateManager(config)
    intermediate_state = state_manager.load_state()
    print(f"Intermediate state after sequential: {intermediate_state}")
    
    # Now test concurrent
    print("Running workers concurrently...")
    with ThreadPoolExecutor(max_workers=2) as executor:
        futures = [executor.submit(worker_function, i+10) for i in range(2)]
        
        for future in futures:
            future.result()
    
    # Check final state
    final_state = state_manager.load_state()
    print(f"Final state: {final_state}")
    print(f"Results: {results}")
    print(f"Errors: {errors}")
    
    # Clean up
    if os.path.exists(config.state_file):
        os.remove(config.state_file)
    
    return len(errors) == 0


def main():
    """Run debug tests"""
    print("Concurrent Access Debug Tests")
    print("=" * 40)
    
    tests = [
        debug_single_operation,
        debug_sequential_operations,
        debug_concurrent_simple
    ]
    
    for test in tests:
        try:
            result = test()
            print(f"✓ {test.__name__}: {'PASSED' if result else 'FAILED'}")
        except Exception as e:
            print(f"✗ {test.__name__}: FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
        print()


if __name__ == "__main__":
    main()
