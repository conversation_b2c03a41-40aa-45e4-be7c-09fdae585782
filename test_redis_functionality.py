#!/usr/bin/env python3
"""
Quick test to verify Redis distributed locking functionality
"""

import sys
import os
import time

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import <PERSON><PERSON>lerConfig, StateManager, RedisLockManager


def test_redis_package():
    """Test if Redis package is now available"""
    print("Testing Redis package availability...")
    
    try:
        import redis
        print("✓ Redis package is available")
        
        # Test basic Redis functionality
        try:
            r = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=2)
            r.ping()
            print("✓ Redis server is running and accessible")
            return True
        except Exception as e:
            print(f"⚠️  Redis server not accessible: {e}")
            print("   (This is expected if Redis server is not running)")
            return False
            
    except ImportError:
        print("✗ Redis package still not available")
        return False


def test_redis_lock_manager():
    """Test Redis lock manager initialization"""
    print("\nTesting Redis lock manager...")
    
    # Test with Redis enabled
    config = CrawlerConfig()
    config.redis_enabled = True
    config.redis_host = "localhost"
    config.redis_port = 6379
    
    try:
        lock_manager = RedisLockManager(config)
        
        if lock_manager.redis_available:
            print("✓ Redis lock manager initialized successfully")
            print("✓ Redis connection established")
            
            # Test lock acquisition
            with lock_manager.acquire_lock("test_lock"):
                print("✓ Redis lock acquired and released successfully")
            
            return True
        else:
            print("⚠️  Redis lock manager fell back to file-based locking")
            print("   (This is expected if Redis server is not running)")
            return True
            
    except Exception as e:
        print(f"✗ Redis lock manager test failed: {e}")
        return False


def test_state_manager_with_redis():
    """Test state manager with Redis enabled"""
    print("\nTesting state manager with Redis...")
    
    config = CrawlerConfig()
    config.redis_enabled = True
    config.state_file = "test_redis_state.json"
    
    try:
        state_manager = StateManager(config)
        
        # Test basic operations
        test_state = {"test": "redis_data", "counter": 42}
        state_manager.save_state(test_state)
        
        loaded_state = state_manager.load_state()
        
        if loaded_state.get("test") == "redis_data" and loaded_state.get("counter") == 42:
            print("✓ State manager with Redis works correctly")
            
            # Clean up
            if os.path.exists(config.state_file):
                os.remove(config.state_file)
            
            return True
        else:
            print(f"✗ State data mismatch: {loaded_state}")
            return False
            
    except Exception as e:
        print(f"✗ State manager test failed: {e}")
        return False


def test_configuration_loading():
    """Test that Redis configuration is loaded correctly"""
    print("\nTesting Redis configuration...")
    
    config = CrawlerConfig.from_file("crawler_config.json")
    
    print(f"Redis enabled: {config.redis_enabled}")
    print(f"Redis host: {config.redis_host}")
    print(f"Redis port: {config.redis_port}")
    print(f"Redis lock timeout: {config.redis_lock_timeout}")
    print(f"Redis lock expire: {config.redis_lock_expire}")
    
    # Verify all Redis settings are present
    redis_settings = [
        'redis_enabled', 'redis_host', 'redis_port', 'redis_password',
        'redis_db', 'redis_lock_timeout', 'redis_lock_expire', 'redis_connection_timeout'
    ]
    
    missing_settings = [setting for setting in redis_settings if not hasattr(config, setting)]
    
    if missing_settings:
        print(f"✗ Missing Redis settings: {missing_settings}")
        return False
    else:
        print("✓ All Redis configuration settings are present")
        return True


def main():
    """Run Redis functionality tests"""
    print("Redis Distributed Locking Functionality Test")
    print("=" * 50)
    
    tests = [
        test_redis_package,
        test_configuration_loading,
        test_redis_lock_manager,
        test_state_manager_with_redis
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ {test.__name__} FAILED with exception: {e}")
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 Redis functionality is working correctly!")
        print("\nTo enable Redis distributed locking:")
        print('1. Start Redis server: redis-server')
        print('2. Set "redis_enabled": true in crawler_config.json')
        print('3. Run multiple crawler instances safely')
    else:
        print("⚠️  Some tests failed, but fallback mechanisms are working")
    
    return passed >= 3  # Allow some tests to fail if Redis server not running


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
