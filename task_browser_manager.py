"""
Task Browser Manager for Web Crawling System
Manages browser instances and tabs with domain-based grouping and task isolation
"""

import threading
import time
import logging
from typing import Dict, Optional, Set
from dataclasses import dataclass, field

from DrissionPage import ChromiumPage, Chromium
from DrissionPage.common import Settings
from DrissionPage import ChromiumOptions

try:
    from improved_forum_crawler import CrawlerConfig, AntiDetectionManager
except ImportError:
    print("Warning: improved_forum_crawler not found, using basic implementations")
    class CrawlerConfig:
        def __init__(self):
            self.headless = False
            self.page_load_delay = 3.0
    class AntiDetectionManager:
        def __init__(self, config):
            self.user_agents = [
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
            ]
        def get_random_user_agent(self):
            return self.user_agents[0]


@dataclass
class BrowserInstance:
    """Represents a browser instance for a specific domain"""
    domain: str
    browser: Chromium
    tabs: Dict[str, ChromiumPage] = field(default_factory=dict)  # task_id -> page
    active_tasks: Set[str] = field(default_factory=set)
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)


@dataclass
class TaskTab:
    """Represents a browser tab for a specific task"""
    task_id: str
    page: ChromiumPage
    post_url: str
    domain: str
    created_at: float = field(default_factory=time.time)
    last_used: float = field(default_factory=time.time)


class TaskBrowserManager:
    """
    Manages browser instances and tabs for crawling tasks
    - Groups tasks by forum domain (one browser per domain)
    - Each task gets its own tab within the domain's browser
    - Automatic cleanup when no tasks are active for a domain
    """
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("task_browser_manager")
        
        # Browser management
        self.browsers: Dict[str, BrowserInstance] = {}  # domain -> BrowserInstance
        self.task_tabs: Dict[str, TaskTab] = {}  # task_id -> TaskTab
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Anti-detection manager
        self.anti_detection = AntiDetectionManager(config)
        
        # Cleanup settings - 大幅增加超时时间以避免过早清理
        self.browser_idle_timeout = 7200  # 2 小时
        self.tab_idle_timeout = 3600     # 1 小时
        
        # Start cleanup thread
        self.cleanup_thread = threading.Thread(
            target=self._cleanup_worker, 
            daemon=True, 
            name="BrowserCleanup"
        )
        self.cleanup_thread.start()
        
        self.logger.info("TaskBrowserManager initialized")
    
    def create_task_tab(self, task_id: str, post_url: str, domain: str) -> ChromiumPage:
        """
        Create a new browser tab for a task
        Returns the ChromiumPage instance for the task
        """
        with self.lock:
            # Check if task already has a tab
            if task_id in self.task_tabs:
                self.logger.warning(f"Task {task_id} already has a tab, returning existing")
                return self.task_tabs[task_id].page
            
            # Get or create browser for domain
            browser_instance = self._get_or_create_browser(domain)
            
            try:
                # Create new tab
                page = browser_instance.browser.new_tab()
                
                # Configure the page
                self._configure_page(page)
                
                # Navigate to the post URL
                self.logger.info(f"Navigating task {task_id} tab to {post_url}")
                page.get(post_url)
                time.sleep(self.config.page_load_delay)
                
                # Create task tab record
                task_tab = TaskTab(
                    task_id=task_id,
                    page=page,
                    post_url=post_url,
                    domain=domain
                )
                
                # Store references
                self.task_tabs[task_id] = task_tab
                browser_instance.tabs[task_id] = page
                browser_instance.active_tasks.add(task_id)
                browser_instance.last_used = time.time()
                
                self.logger.info(f"Created tab for task {task_id} on domain {domain}")
                return page
                
            except Exception as e:
                self.logger.error(f"Failed to create tab for task {task_id}: {e}")
                raise
    
    def get_task_tab(self, task_id: str) -> Optional[ChromiumPage]:
        """Get the browser tab for a specific task"""
        with self.lock:
            task_tab = self.task_tabs.get(task_id)
            if task_tab:
                task_tab.last_used = time.time()
                # Update browser last used time
                browser_instance = self.browsers.get(task_tab.domain)
                if browser_instance:
                    browser_instance.last_used = time.time()
                return task_tab.page
            return None
    
    def close_task_tab(self, task_id: str):
        """Close the browser tab for a specific task"""
        with self.lock:
            task_tab = self.task_tabs.get(task_id)
            if not task_tab:
                self.logger.warning(f"No tab found for task {task_id}")
                return
            
            domain = task_tab.domain
            browser_instance = self.browsers.get(domain)
            
            try:
                # Close the tab
                task_tab.page.close()
                self.logger.info(f"Closed tab for task {task_id}")
                
            except Exception as e:
                self.logger.error(f"Error closing tab for task {task_id}: {e}")
            
            finally:
                # Clean up references
                del self.task_tabs[task_id]
                
                if browser_instance:
                    browser_instance.tabs.pop(task_id, None)
                    browser_instance.active_tasks.discard(task_id)
                    browser_instance.last_used = time.time()
                    
                    # If no more tasks, schedule browser for cleanup
                    if not browser_instance.active_tasks:
                        self.logger.info(f"No more tasks for domain {domain}, browser will be cleaned up")
    
    def get_active_domains(self) -> Set[str]:
        """Get all domains with active browsers"""
        with self.lock:
            return set(self.browsers.keys())
    
    def get_active_tasks_for_domain(self, domain: str) -> Set[str]:
        """Get all active task IDs for a specific domain"""
        with self.lock:
            browser_instance = self.browsers.get(domain)
            return browser_instance.active_tasks.copy() if browser_instance else set()
    
    def get_total_active_tasks(self) -> int:
        """Get total number of active tasks across all domains"""
        with self.lock:
            return len(self.task_tabs)
    
    def cleanup_all(self):
        """Close all browsers and tabs"""
        with self.lock:
            self.logger.info("Cleaning up all browsers and tabs")
            
            # Close all task tabs
            for task_id in list(self.task_tabs.keys()):
                self.close_task_tab(task_id)
            
            # Close all browsers
            for domain, browser_instance in list(self.browsers.items()):
                try:
                    browser_instance.browser.quit()
                    self.logger.info(f"Closed browser for domain {domain}")
                except Exception as e:
                    self.logger.error(f"Error closing browser for domain {domain}: {e}")
            
            self.browsers.clear()
            self.task_tabs.clear()
    
    def _get_or_create_browser(self, domain: str) -> BrowserInstance:
        """Get existing browser for domain or create a new one"""
        browser_instance = self.browsers.get(domain)
        
        if browser_instance:
            browser_instance.last_used = time.time()
            return browser_instance
        
        # Create new browser for domain
        self.logger.info(f"Creating new browser for domain {domain}")
        
        try:
            # Configure browser options
            browser_options = self._get_browser_options()
            browser = Chromium(addr_or_opts=browser_options)
            
            # Create browser instance record
            browser_instance = BrowserInstance(
                domain=domain,
                browser=browser
            )
            
            self.browsers[domain] = browser_instance
            self.logger.info(f"Created browser for domain {domain}")
            
            return browser_instance
            
        except Exception as e:
            self.logger.error(f"Failed to create browser for domain {domain}: {e}")
            raise
    
    def _get_browser_options(self) -> ChromiumOptions:
        """Get browser configuration options optimized for Cloudflare bypass"""
        options = ChromiumOptions()

        # 强制使用有头模式以绕过 Cloudflare
        options.headless(False)

        # 使用推荐的 Cloudflare 绕过 User-Agent
        cloudflare_user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        options.set_user_agent(cloudflare_user_agent)

        # 最小化的反检测参数 - 只保留必要的
        options.set_argument("--no-sandbox")
        options.set_argument("--disable-blink-features=AutomationControlled")

        # 保持真实浏览器功能 - 不禁用 JavaScript 和图片（Cloudflare 需要）
        # options.set_argument("--disable-javascript")  # 注释掉 - Cloudflare 需要 JS
        # options.set_argument("--disable-images")      # 注释掉 - 验证码需要图片

        # 窗口和语言设置
        options.set_argument("--window-size=1366,768")  # 常见分辨率
        options.set_argument("--lang=zh-CN,zh;q=0.9,en;q=0.8")

        # 独立的用户数据目录
        options.set_argument("--user-data-dir=./chrome_profile_cf")

        return options
    
    def _configure_page(self, page: ChromiumPage):
        """Configure a page with anti-detection settings"""
        try:
            # Set random user agent
            user_agent = self.anti_detection.get_random_user_agent()
            page.set.user_agent(user_agent)

            # Note: Skip timeout configuration as it varies between DrissionPage versions
            # The browser options already handle most configuration

            self.logger.debug("Page configured successfully")

        except Exception as e:
            self.logger.warning(f"Failed to configure page: {e}")
    
    def _cleanup_worker(self):
        """Background worker to clean up idle browsers and tabs"""
        while True:
            try:
                time.sleep(60)  # Check every minute
                self._cleanup_idle_resources()
                
            except Exception as e:
                self.logger.error(f"Error in cleanup worker: {e}")
    
    def _cleanup_idle_resources(self):
        """Clean up idle browsers and tabs"""
        current_time = time.time()
        
        with self.lock:
            # Clean up idle tabs
            idle_tasks = []
            for task_id, task_tab in self.task_tabs.items():
                if current_time - task_tab.last_used > self.tab_idle_timeout:
                    idle_tasks.append(task_id)
            
            for task_id in idle_tasks:
                self.logger.info(f"Cleaning up idle task tab: {task_id}")
                self.close_task_tab(task_id)
            
            # Clean up idle browsers (only if no active tasks)
            idle_domains = []
            for domain, browser_instance in self.browsers.items():
                if (not browser_instance.active_tasks and 
                    current_time - browser_instance.last_used > self.browser_idle_timeout):
                    idle_domains.append(domain)
            
            for domain in idle_domains:
                self.logger.info(f"Cleaning up idle browser for domain: {domain}")
                try:
                    self.browsers[domain].browser.quit()
                    del self.browsers[domain]
                except Exception as e:
                    self.logger.error(f"Error cleaning up browser for {domain}: {e}")
