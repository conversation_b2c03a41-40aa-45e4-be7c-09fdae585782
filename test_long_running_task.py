#!/usr/bin/env python3
"""
测试长时间运行任务
验证修复后的配置是否能保持任务持续运行
"""

import requests
import time
import json

API_BASE = "http://127.0.0.1:8001"

def test_long_running_task():
    """测试长时间运行任务"""
    print("🧪 测试长时间运行任务")
    print("=" * 50)
    print("此测试将验证:")
    print("- 浏览器标签页不会被过早清理")
    print("- 监控循环能持续运行")
    print("- 页面连接保持稳定")
    print("- 任务状态正确更新")
    print("=" * 50)
    
    # 创建任务
    task_data = {
        "post_url": "https://www.nodeseek.com/post-393807-1#3",
        "forum_domain": "nodeseek.com",
        "monitor_interval": 60,  # 1分钟间隔
        "ai_analysis_enabled": True
    }
    
    print("1. 创建长时间运行任务...")
    response = requests.post(f"{API_BASE}/tasks", json=task_data)
    
    if response.status_code != 200:
        print(f"❌ 任务创建失败: {response.text}")
        return False
    
    task = response.json()
    task_id = task["id"]
    print(f"✅ 任务创建成功: {task_id}")
    
    # 启动任务
    print(f"\n2. 启动任务...")
    response = requests.put(f"{API_BASE}/tasks/{task_id}/start")
    
    if response.status_code != 200:
        print(f"❌ 任务启动失败: {response.text}")
        return False
    
    print("✅ 任务启动成功")
    print("🔍 现在应该有一个可见的浏览器窗口打开")
    print("📋 新的配置:")
    print("   - 浏览器空闲超时: 2小时")
    print("   - 标签页空闲超时: 1小时")
    print("   - 清理检查间隔: 5分钟")
    print("   - 监控间隔: 1分钟")
    print("   - 连续错误重试: 3次")
    
    # 监控任务运行 10 分钟
    print(f"\n3. 监控任务运行 10 分钟...")
    print("   每分钟检查一次状态")
    
    start_time = time.time()
    check_count = 0
    
    try:
        for i in range(10):  # 10分钟
            time.sleep(60)  # 等待1分钟
            check_count += 1
            
            # 检查任务状态
            response = requests.get(f"{API_BASE}/tasks/{task_id}")
            
            if response.status_code == 200:
                task_status = response.json()
                elapsed = time.time() - start_time
                
                print(f"\n📊 检查 {check_count}/10 (运行时间: {elapsed/60:.1f}分钟):")
                print(f"   任务状态: {task_status['status']}")
                print(f"   开始时间: {task_status.get('started_at', 'N/A')}")
                print(f"   最后检查: {task_status.get('last_check_at', 'N/A')}")
                print(f"   评论数量: {task_status.get('comment_count', 0)}")
                print(f"   发现闪购: {task_status.get('flash_sales_found', 0)}")
                
                if task_status.get('error_message'):
                    print(f"   ❌ 错误: {task_status['error_message']}")
                    print("   任务遇到错误，提前结束测试")
                    break
                
                if task_status['status'] != 'running':
                    print(f"   ⚠️ 任务状态不是 running: {task_status['status']}")
                    print("   任务可能已停止，提前结束测试")
                    break
                
                print(f"   ✅ 任务正常运行")
                
            else:
                print(f"   ❌ 无法获取任务状态: {response.status_code}")
                break
        
        print(f"\n🎉 测试完成！任务成功运行了 {check_count} 分钟")
        
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断测试")
    
    # 停止并清理任务
    print(f"\n4. 停止并清理任务...")
    
    # 停止任务
    response = requests.put(f"{API_BASE}/tasks/{task_id}/stop")
    if response.status_code == 200:
        print("✅ 任务停止成功")
    else:
        print(f"⚠️ 任务停止失败: {response.text}")
    
    # 删除任务
    response = requests.delete(f"{API_BASE}/tasks/{task_id}")
    if response.status_code == 200:
        print("✅ 任务删除成功")
    else:
        print(f"⚠️ 任务删除失败: {response.text}")
    
    return True


def test_multiple_tasks():
    """测试多个任务同时运行"""
    print("\n🧪 测试多个任务同时运行")
    print("=" * 50)
    
    task_ids = []
    
    # 创建3个任务
    for i in range(3):
        task_data = {
            "post_url": f"https://www.nodeseek.com/post-39380{i+1}-1#3",
            "forum_domain": "nodeseek.com",
            "monitor_interval": 90,  # 1.5分钟间隔
            "ai_analysis_enabled": True
        }
        
        print(f"创建任务 {i+1}/3...")
        response = requests.post(f"{API_BASE}/tasks", json=task_data)
        
        if response.status_code == 200:
            task = response.json()
            task_ids.append(task["id"])
            print(f"✅ 任务 {i+1} 创建成功: {task['id'][:8]}")
        else:
            print(f"❌ 任务 {i+1} 创建失败")
    
    if not task_ids:
        print("❌ 没有任务创建成功")
        return False
    
    # 启动所有任务
    print(f"\n启动 {len(task_ids)} 个任务...")
    for i, task_id in enumerate(task_ids):
        response = requests.put(f"{API_BASE}/tasks/{task_id}/start")
        if response.status_code == 200:
            print(f"✅ 任务 {i+1} 启动成功")
        else:
            print(f"❌ 任务 {i+1} 启动失败")
        time.sleep(2)  # 间隔启动
    
    # 监控5分钟
    print(f"\n监控所有任务运行 5 分钟...")
    
    try:
        for check in range(5):
            time.sleep(60)
            
            print(f"\n📊 检查 {check+1}/5:")
            
            # 检查所有任务状态
            for i, task_id in enumerate(task_ids):
                response = requests.get(f"{API_BASE}/tasks/{task_id}")
                if response.status_code == 200:
                    task_status = response.json()
                    print(f"   任务 {i+1}: {task_status['status']} - 评论: {task_status.get('comment_count', 0)}")
                else:
                    print(f"   任务 {i+1}: 无法获取状态")
    
    except KeyboardInterrupt:
        print(f"\n⚠️ 用户中断测试")
    
    # 清理所有任务
    print(f"\n清理所有任务...")
    for i, task_id in enumerate(task_ids):
        requests.put(f"{API_BASE}/tasks/{task_id}/stop")
        requests.delete(f"{API_BASE}/tasks/{task_id}")
        print(f"✅ 任务 {i+1} 清理完成")
    
    return True


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试长时间运行任务')
    parser.add_argument('--mode', choices=['single', 'multiple'], default='single',
                       help='测试模式')
    
    args = parser.parse_args()
    
    print("🕐 长时间运行任务测试")
    print("=" * 30)
    
    if args.mode == 'single':
        success = test_long_running_task()
    elif args.mode == 'multiple':
        success = test_multiple_tasks()
    
    if success:
        print("\n🎉 所有测试完成！")
        print("\n📋 修复总结:")
        print("✅ 浏览器空闲超时: 5分钟 → 2小时")
        print("✅ 标签页空闲超时: 3分钟 → 1小时")
        print("✅ 清理检查间隔: 1分钟 → 5分钟")
        print("✅ 监控循环错误重试: 增加3次重试机制")
        print("✅ 页面连接检查: 每次监控都更新使用时间")
        print("✅ 连接断开恢复: 自动重新导航到页面")
    else:
        print("\n❌ 测试失败")


if __name__ == "__main__":
    main()
