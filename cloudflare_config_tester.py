#!/usr/bin/env python3
"""
Cloudflare Configuration Tester
基于你的 cloudflare 方法来测试不同的 user-agent 和配置
"""

import time
import random
import sys
import logging
from typing import List, Dict
from DrissionPage import ChromiumPage, ChromiumOptions

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CloudflareConfigTester:
    """测试不同配置对 Cloudflare 的效果"""
    
    def __init__(self):
        self.logger = logging.getLogger("cf_tester")
        
        # 不同类型的 User-Agent 进行测试
        self.user_agents = {
            "chrome_120_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "chrome_119_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "chrome_118_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/118.0.0.0 Safari/537.36",
            "chrome_120_mac": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "firefox_121_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0",
            "firefox_120_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/120.0",
            "edge_120_windows": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0",
            "chrome_old_stable": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        }
        
        # 不同的配置策略
        self.configs = {
            "minimal": {
                "args": ["--no-sandbox", "--disable-blink-features=AutomationControlled"],
                "description": "最小配置"
            },
            "stealth": {
                "args": [
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--disable-dev-shm-usage"
                ],
                "description": "隐身配置"
            },
            "realistic": {
                "args": [
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled",
                    "--exclude-switches=enable-automation",
                    "--enable-features=NetworkService",
                    "--disable-features=VizDisplayCompositor"
                ],
                "description": "真实浏览器配置"
            },
            "conservative": {
                "args": [
                    "--no-sandbox",
                    "--disable-blink-features=AutomationControlled"
                ],
                "description": "保守配置"
            }
        }
    
    def create_test_options(self, user_agent: str, config_name: str) -> ChromiumOptions:
        """创建测试用的浏览器选项"""
        options = ChromiumOptions()
        
        # 使用有头模式
        options.headless(False)
        
        # 设置用户代理
        options.set_user_agent(user_agent)
        
        # 应用配置
        config = self.configs[config_name]
        for arg in config["args"]:
            options.set_argument(arg)
        
        # 基本设置
        options.set_argument("--window-size=1920,1080")
        options.set_argument("--lang=zh-CN,zh;q=0.9,en;q=0.8")
        
        return options
    
    def cloudflare_handler(self, tab: ChromiumPage) -> bool:
        """你的 Cloudflare 处理方法（稍作修改）"""
        max_retries = 5
        retries = 0
        
        while retries < max_retries:
            try:
                self.logger.info(f"Cloudflare 处理尝试 {retries + 1}/{max_retries}")
                
                if 'Just a moment' in tab.title or '请稍候' in tab.title:
                    self.logger.info("检测到 Cloudflare 挑战页面")
                    
                    # 查找主要内容区域
                    if tab.s_ele('x://div[@class="main-content"]'):
                        self.logger.info("找到 main-content 区域")
                        mainDiv = tab.ele('x://div[@class="main-content"]/div')
                        
                        # 检查是否有输入框
                        if mainDiv.ele('x:/div/input', timeout=0.1):
                            self.logger.info("找到输入框，点击")
                            tab.s_ele('x:/div/input').click()
                        
                        # 查找 iframe
                        iframe = mainDiv.ele('x:/div/div').sr('x://iframe')
                        if iframe:
                            self.logger.info("找到 iframe")
                            body = iframe.ele('x://body')
                            if body:
                                self.logger.info("找到 iframe body")
                                checkbox = body.sr('x://input[@type="checkbox"]')
                                if checkbox:
                                    self.logger.info("找到复选框，点击")
                                    checkbox.click()
                                    
                                    # 等待处理
                                    time.sleep(random.uniform(3, 6))
                                    
                                    # 检查是否成功
                                    if 'Just a moment' not in tab.title and '请稍候' not in tab.title:
                                        self.logger.info("✅ Cloudflare 挑战成功通过！")
                                        return True
                                    break
                                else:
                                    self.logger.warning("未找到复选框")
                            else:
                                self.logger.warning("未找到 iframe body")
                        else:
                            self.logger.warning("未找到 iframe")
                    else:
                        self.logger.warning("未找到 main-content")
                else:
                    self.logger.info("✅ 没有 Cloudflare 挑战，直接通过")
                    return True
                    
            except Exception as e:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_number = exc_tb.tb_lineno
                self.logger.error(f"Cloudflare 处理异常 at line {line_number}: {e}")
                retries += 1
                time.sleep(2)
        
        self.logger.warning("❌ Cloudflare 挑战处理失败")
        return False
    
    def test_configuration(self, ua_name: str, config_name: str, url: str = "https://www.nodeseek.com/") -> Dict:
        """测试特定配置"""
        self.logger.info(f"🧪 测试配置: {ua_name} + {config_name}")
        
        result = {
            "user_agent": ua_name,
            "config": config_name,
            "success": False,
            "cloudflare_detected": False,
            "cloudflare_resolved": False,
            "final_title": "",
            "error": None
        }
        
        try:
            # 创建浏览器选项
            user_agent = self.user_agents[ua_name]
            options = self.create_test_options(user_agent, config_name)
            
            # 创建页面
            page = ChromiumPage(addr_or_opts=options)
            
            # 导航到目标页面
            self.logger.info(f"导航到 {url}")
            page.get(url)
            
            # 等待初始加载
            time.sleep(5)
            
            # 检查是否遇到 Cloudflare
            initial_title = page.title
            self.logger.info(f"初始页面标题: {initial_title}")
            
            if 'Just a moment' in initial_title or '请稍候' in initial_title:
                result["cloudflare_detected"] = True
                self.logger.info("检测到 Cloudflare 挑战")
                
                # 使用你的方法处理 Cloudflare
                cf_success = self.cloudflare_handler(page)
                result["cloudflare_resolved"] = cf_success
                
                if cf_success:
                    # 等待页面完全加载
                    time.sleep(5)
                    final_title = page.title
                    result["final_title"] = final_title
                    result["success"] = True
                    self.logger.info(f"✅ 成功！最终标题: {final_title}")
                else:
                    result["final_title"] = page.title
                    self.logger.warning(f"❌ Cloudflare 处理失败")
            else:
                # 没有 Cloudflare 挑战
                result["success"] = True
                result["final_title"] = initial_title
                self.logger.info("✅ 没有遇到 Cloudflare 挑战")
            
            # 保持浏览器打开一段时间供观察
            self.logger.info("保持浏览器打开 15 秒供观察...")
            time.sleep(15)
            
            page.close()
            
        except Exception as e:
            result["error"] = str(e)
            self.logger.error(f"❌ 测试失败: {e}")
        
        return result
    
    def run_comprehensive_test(self):
        """运行全面测试"""
        self.logger.info("🚀 开始全面 Cloudflare 配置测试")
        
        results = []
        
        # 测试不同的组合
        test_combinations = [
            ("chrome_120_windows", "minimal"),
            ("chrome_119_windows", "stealth"),
            ("chrome_118_windows", "realistic"),
            ("chrome_120_mac", "conservative"),
            ("firefox_121_windows", "minimal"),
            ("edge_120_windows", "stealth"),
            ("chrome_old_stable", "realistic"),
        ]
        
        for ua_name, config_name in test_combinations:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"测试组合: {ua_name} + {config_name}")
            self.logger.info(f"{'='*60}")
            
            result = self.test_configuration(ua_name, config_name)
            results.append(result)
            
            # 打印结果
            self.print_result(result)
            
            # 等待一段时间再进行下一个测试
            self.logger.info("等待 10 秒后进行下一个测试...")
            time.sleep(10)
        
        # 打印总结
        self.print_summary(results)
        
        return results
    
    def print_result(self, result: Dict):
        """打印单个测试结果"""
        status = "✅ 成功" if result["success"] else "❌ 失败"
        cf_status = "检测到" if result["cloudflare_detected"] else "未检测到"
        cf_resolved = "已解决" if result["cloudflare_resolved"] else "未解决"
        
        print(f"""
📊 测试结果:
   User-Agent: {result['user_agent']}
   配置: {result['config']}
   状态: {status}
   Cloudflare: {cf_status}
   Cloudflare 解决: {cf_resolved}
   最终标题: {result['final_title'][:50]}...
   错误: {result.get('error', '无')}
        """)
    
    def print_summary(self, results: List[Dict]):
        """打印测试总结"""
        print(f"\n{'='*80}")
        print("🎯 测试总结")
        print(f"{'='*80}")
        
        successful = [r for r in results if r["success"]]
        cf_resolved = [r for r in results if r["cloudflare_resolved"]]
        
        print(f"总测试数: {len(results)}")
        print(f"成功数: {len(successful)}")
        print(f"Cloudflare 解决数: {len(cf_resolved)}")
        
        if successful:
            print("\n✅ 成功的配置:")
            for result in successful:
                print(f"   - {result['user_agent']} + {result['config']}")
        
        if cf_resolved:
            print("\n🛡️ 成功解决 Cloudflare 的配置:")
            for result in cf_resolved:
                print(f"   - {result['user_agent']} + {result['config']}")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Cloudflare Configuration Tester')
    parser.add_argument('--ua', choices=list(CloudflareConfigTester().user_agents.keys()),
                       help='指定测试的 User-Agent')
    parser.add_argument('--config', choices=list(CloudflareConfigTester().configs.keys()),
                       help='指定测试的配置')
    parser.add_argument('--url', default='https://www.nodeseek.com/',
                       help='测试的 URL')
    
    args = parser.parse_args()
    
    tester = CloudflareConfigTester()
    
    if args.ua and args.config:
        # 测试单个配置
        print(f"🎯 测试单个配置: {args.ua} + {args.config}")
        result = tester.test_configuration(args.ua, args.config, args.url)
        tester.print_result(result)
    else:
        # 运行全面测试
        tester.run_comprehensive_test()


if __name__ == "__main__":
    main()
