�f�5            �f�5            �f�5            D�Q�           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (��10��X          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (��10s�?           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (��10��h��           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (��10��0�`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (��10_�gl�          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (��10νil6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (��10�\�a          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (��10�HA�W	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (��10/L��� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (��10�u*�1           	39_config
��؈��O�ԓ ��1���~           	39_configf
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1�Q�b� 
          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����_�x�           	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1���;          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1
��՛�����ԓ ��1
���Åօ�C�ԓ ��1
�����Ӆ���ԓ ��1
��������_�ԓ ��1fYr��          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1
��՛�����ԓ ��1
���Åօ�C�ԓ ��1
�����Ӆ���ԓ ��1
��������_�ԓ ��1
�����������I ��1
�������I ��1
ʒ���қ�C��I ��1
���޾���,��I ��1
���������I ��1�4�P          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1
��՛�����ԓ ��1
���Åօ�C�ԓ ��1
�����Ӆ���ԓ ��1
��������_�ԓ ��1
�����������I ��1
�������I ��1
ʒ���қ�C��I ��1
���޾���,��I ��1
���������I ��1
������t�ԓ ��1
��������k�ԓ ��1
գ��������ԓ ��1
��ר�ٳ���ԓ ��1
ෛ�������ԓ ��1
������Ʉ��ԓ ��1��o�          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1
��՛�����ԓ ��1
���Åօ�C�ԓ ��1
�����Ӆ���ԓ ��1
��������_�ԓ ��1
�����������I ��1
�������I ��1
ʒ���қ�C��I ��1
���޾���,��I ��1
���������I ��1
������t�ԓ ��1
��������k�ԓ ��1
գ��������ԓ ��1
��ר�ٳ���ԓ ��1
ෛ�������ԓ ��1
������Ʉ��ԓ ��1
"��ї�Z�ԓ ��1(Ȏ�������
#�򖐩�����ԓ ��1(Ȏ�������
#�ɕԺ����ԓ ��1(Ȏ�������q��          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    񍸷ߐ�$


   ?ShoppingUserOther  (��10G��4           20_1_1
1F9%��       .   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_BatterySaverMode
IPH_BatterySaverMode��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo��4_IPH_GMCCastStartStop
IPH_GMCCastStartStop��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��4_IPH_LiveCaption
IPH_LiveCaption��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_ProfileSwitch
IPH_ProfileSwitch��4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��'4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop��Qb�� C           s�X�&C          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��l�ID          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSióE          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2�+~GF          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��{*G          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�(�(H          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING9��uAI          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�0w'&J          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�*�3&K          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�/�&L          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONV��:IM          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSQ'�2IN          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�_� IO          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��G�P          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2^�EGQ          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS>��*R          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��&�(S          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING{�[AT          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��6:�U          021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     @b�RV          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH884Te6N7sU_n9s-Y8qbwCFWexixBK_aGI2_uqiuvLp2j3rvBtMaCLaKDypJCV2JvVTUi9WS1oHENghOAg vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:54 GMT expires:Wed, 16 Jul 2025 03:50:54 GMT accept-ranges:bytes x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONn����W          021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 4 9 6 2 7 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x ����������������� �� �� � � � � ����������������������


     R�K�X          021_download,b4062852-0251-46cf-af3f-51e83918b64a�
�
$b4062852-0251-46cf-af3f-51e83918b64a
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj       r       x �ٺ�������������� �� � � � � � � ����������������������


     7=&��Y          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH89LNUFpa5rtK9QNDPB6ZDIKV5_NaxyT6sMAn5ZCl4JkHd9INfd6YdW-aRE4mQP0GhISTqKp2wn8B-Dq6Q expires:Wed, 16 Jul 2025 03:50:54 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:54 GMT x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSUPI�Z          021_download,b4062852-0251-46cf-af3f-51e83918b64a�
�
$b4062852-0251-46cf-af3f-51e83918b64a
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   i   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 3 0 1 2 . c r d o w n l o a d   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 4 0 6 2 8 5 2 - 0 2 5 1 - 4 6 c f - a f 3 f - 5 1 e 8 3 9 1 8 b 6 4 a   x �ٺ�������������� �� �� � � � � ����������������������


     �"H�][          021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 4 9 6 2 7 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x������������������� �� �� � � � � ����������������������


     021_download,b4062852-0251-46cf-af3f-51e83918b64a�
�
$b4062852-0251-46cf-af3f-51e83918b64a
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 4 0 6 2 8 5 2 - 0 2 5 1 - 4 6 c f - a f 3 f - 5 1 e 8 3 9 1 8 b 6 4 a   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 4 0 6 2 8 5 2 - 0 2 5 1 - 4 6 c f - a f 3 f - 5 1 e 8 3 9 1 8 b 6 4 a   xڎ�ٺ����޻���� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     %.o�]          &9_b4062852-0251-46cf-af3f-51e83918b64a�	$b4062852-0251-46cf-af3f-51e83918b64a��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\b4062852-0251-46cf-af3f-51e83918b64a8��ߐ�@�֚�ߐ�HPϟ�/Xڎ`�֚�ߐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH89LNUFpa5rtK9QNDPB6ZDIKV5_NaxyT6sMAn5ZCl4JkHd9INfd6YdW-aRE4mQP0GhISTqKp2wn8B-Dq6Q expires:Wed, 16 Jul 2025 03:50:54 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:54 GMT x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS���3^          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2���*_          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2D/���`          021_download,b4062852-0251-46cf-af3f-51e83918b64a�
�
$b4062852-0251-46cf-af3f-51e83918b64a
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 4 0 6 2 8 5 2 - 0 2 5 1 - 4 6 c f - a f 3 f - 5 1 e 8 3 9 1 8 b 6 4 a   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ b 4 0 6 2 8 5 2 - 0 2 5 1 - 4 6 c f - a f 3 f - 5 1 e 8 3 9 1 8 b 6 4 a   xڎ�ٺ����޻���� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     ���H> a           021_download,b4062852-0251-46cf-af3f-51e83918b64a��I�3b          021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 4 9 6 2 7 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x������������������� �� �� � � � � ����������������������


     �ə�d          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-zwvu9OgOEKHFySBSn6otPofaMolEOUSu3cQAHK4oh0IjmUXsh45tY6ovX21GJHnOAKrP_4MdEr6RS5w ���4�vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:55 GMT expires:Wed, 16 Jul 2025 03:50:55 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V20�(e          021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 8 6 4 4 5 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   x ����������������� �� �� � � � � ����������������������


     021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 4 9 6 2 7 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x������������������ �!��Q�k���֧*��:{`B��aOW�+V�@�� �� � � � � ����������������������


     �Y�g          021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     �m��bh          &9_10b6512c-1143-4190-ab59-5aa70e90338a�	$10b6512c-1143-4190-ab59-5aa70e90338a��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\10b6512c-1143-4190-ab59-5aa70e90338a8��ߐ�@��»ߐ�HPϟ�/X�`��»ߐ�p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH884Te6N7sU_n9s-Y8qbwCFWexixBK_aGI2_uqiuvLp2j3rvBtMaCLaKDypJCV2JvVTUi9WS1oHENghOAg vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:54 GMT expires:Wed, 16 Jul 2025 03:50:54 GMT accept-ranges:bytes x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��5eGi          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�R�4Gj          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��`��k          021_download,10b6512c-1143-4190-ab59-5aa70e90338a�
�
$10b6512c-1143-4190-ab59-5aa70e90338a
�����؀��"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=**********&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 0 b 6 5 1 2 c - 1 1 4 3 - 4 1 9 0 - a b 5 9 - 5 a a 7 0 e 9 0 3 3 8 a   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     S4�> l           021_download,10b6512c-1143-4190-ab59-5aa70e90338a�ȼ�Em          021_download,02d0c16c-ef9c-4ac8-916b-876af5771b55�
�
$02d0c16c-ef9c-4ac8-916b-876af5771b55
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��ͭ������������� �� � � � � � � ����������������������


     021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 8 6 4 4 5 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   x�������������������� �� �� � � � � ����������������������


     ���6�o          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-TO4gRJez4IxdhW1-hdEO-O4gTrcNFjKm-qDQGdtrSnYlKFGT_QEOemQHXxpy7H0wfwq4fYAcekdXkBg vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:55 GMT expires:Wed, 16 Jul 2025 03:50:55 GMT accept-ranges:bytes x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSa��Tp          021_download,02d0c16c-ef9c-4ac8-916b-876af5771b55�
�
$02d0c16c-ef9c-4ac8-916b-876af5771b55
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  2 2 7 0 1 9 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d 0 c 1 6 c - e f 9 c - 4 a c 8 - 9 1 6 b - 8 7 6 a f 5 7 7 1 b 5 5   x����ͭ������������� \�S�]���s�b0��l?�D}�F��.�"&�r�� �� � � � � ����������������������


     021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 8 6 4 4 5 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   x�������������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     �u�r          021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   x�����������έ��� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     G�/s          &9_099829b4-625c-4ad0-bcb2-3c3537c7bce1�	$099829b4-625c-4ad0-bcb2-3c3537c7bce1��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\099829b4-625c-4ad0-bcb2-3c3537c7bce18��ߐ�@����ߐ�HPϟ�/X���`����ߐ�p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-zwvu9OgOEKHFySBSn6otPofaMolEOUSu3cQAHK4oh0IjmUXsh45tY6ovX21GJHnOAKrP_4MdEr6RS5w vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:55 GMT expires:Wed, 16 Jul 2025 03:50:55 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2 ���*t          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�l��*u          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING����v          021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1�
�
$099829b4-625c-4ad0-bcb2-3c3537c7bce1
�����؀��"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 9 9 8 2 9 b 4 - 6 2 5 c - 4 a d 0 - b c b 2 - 3 c 3 5 3 7 c 7 b c e 1   x�����������έ��� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     1�w> w           021_download,099829b4-625c-4ad0-bcb2-3c3537c7bce1 �+��x          021_download,02d0c16c-ef9c-4ac8-916b-876af5771b55�
�
$02d0c16c-ef9c-4ac8-916b-876af5771b55
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d 0 c 1 6 c - e f 9 c - 4 a c 8 - 9 1 6 b - 8 7 6 a f 5 7 7 1 b 5 5   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d 0 c 1 6 c - e f 9 c - 4 a c 8 - 9 1 6 b - 8 7 6 a f 5 7 7 1 b 5 5   x����ͭ����έ��� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     ��/�y          &9_02d0c16c-ef9c-4ac8-916b-876af5771b55�	$02d0c16c-ef9c-4ac8-916b-876af5771b55��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\02d0c16c-ef9c-4ac8-916b-876af5771b558��ߐ�@����ߐ�HPϟ�/X��`����ߐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-TO4gRJez4IxdhW1-hdEO-O4gTrcNFjKm-qDQGdtrSnYlKFGT_QEOemQHXxpy7H0wfwq4fYAcekdXkBg vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:55 GMT expires:Wed, 16 Jul 2025 03:50:55 GMT accept-ranges:bytes x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS"6^(z          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�|�5({          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@ HPϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�S:��|          021_download,02d0c16c-ef9c-4ac8-916b-876af5771b55�
�
$02d0c16c-ef9c-4ac8-916b-876af5771b55
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d 0 c 1 6 c - e f 9 c - 4 a c 8 - 9 1 6 b - 8 7 6 a f 5 7 7 1 b 5 5   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 2 d 0 c 1 6 c - e f 9 c - 4 a c 8 - 9 1 6 b - 8 7 6 a f 5 7 7 1 b 5 5   x����ͭ����έ��� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     ���> }           021_download,02d0c16c-ef9c-4ac8-916b-876af5771b55
��~          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ��ѭ������������� �� � � � � � � ����������������������


     �]�#�          021_download,a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�
�
$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97
�����؀��"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj       r       x ��ѭ������������� �� � � � � � � ����������������������


     �=�B�          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-MbIO-ZEa49EFSC0Wk4jmiiJ6knwJuZaVigkbxNZpnXW2x2qB-R4djWSHj7SPYN-Cg71bwzuC3MXaXEw accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�r�$S�          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@ HPϟ�/X ` p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-y4gGH836pGEtQza1V-e8C_gKb0gKGTEUnoS_lXnP-cl2sKR1bMxDh3XNs6xyjE8LUMK_JqgYB7P2hNA vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT accept-ranges:bytes x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGD�xy��          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 4 0 1 3 8 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   x ��ѭ������������� �� �� � � � � ����������������������


     n���          021_download,a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�
�
$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97
�����؀��"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  9 7 0 9 6 2 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 8 0 9 9 f 9 e - 0 e 8 c - 4 1 d e - 9 e 7 d - 9 f 1 c 7 d 2 2 a f 9 7   x�&��ѭ������������� ��4�z��2���K"�\kd�ɔ\AZWQ�� �� � � � � ����������������������


     ��/��          021_download,a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�
�
$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97
�����؀��"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 8 0 9 9 f 9 e - 0 e 8 c - 4 1 d e - 9 e 7 d - 9 f 1 c 7 d 2 2 a f 9 7   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 8 0 9 9 f 9 e - 0 e 8 c - 4 1 d e - 9 e 7 d - 9 f 1 c 7 d 2 2 a f 9 7   x�&��ѭ����ҭ��� ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     `��b�          &9_a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�	$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\a8099f9e-0e8c-41de-9e7d-9f1c7d22af978��ߐ�@�˼ߐ�HPϟ�/X�&`�˼ߐ�p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-y4gGH836pGEtQza1V-e8C_gKb0gKGTEUnoS_lXnP-cl2sKR1bMxDh3XNs6xyjE8LUMK_JqgYB7P2hNA vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT accept-ranges:bytes x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGM L�A�          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONv�6�A�          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�i#��          021_download,a8099f9e-0e8c-41de-9e7d-9f1c7d22af97�
�
$a8099f9e-0e8c-41de-9e7d-9f1c7d22af97
�����؀��"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 8 0 9 9 f 9 e - 0 e 8 c - 4 1 d e - 9 e 7 d - 9 f 1 c 7 d 2 2 a f 9 7   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 8 0 9 9 f 9 e - 0 e 8 c - 4 1 d e - 9 e 7 d - 9 f 1 c 7 d 2 2 a f 9 7   x�&��ѭ����ҭ��� ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     C�> �           021_download,a8099f9e-0e8c-41de-9e7d-9f1c7d22af97O��<Q�          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 4 0 1 3 8 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   x��]��ѭ������������� �� �� � � � � ����������������������


     021_download,dabc247e-ad49-4807-a753-3d3359c34665�
�
$dabc247e-ad49-4807-a753-3d3359c34665
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ��֭������������� �� � � � � � � ����������������������


     6ޟ�|�          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88LHFOyOC08TBFDGR4X498MjgV3vVDBN9WDyZi39lF4gAICb9M1Yvty3hObWk-nyYrPTCQq_9qIKnSg1A vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT accept-ranges:bytes x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION{�і�          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  6 4 0 1 3 8 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   x�֧��ѭ������������� 'Ok-����(���e��e�Vs`�x�lO�Rla�� �� � � � � ����������������������


     021_download,dabc247e-ad49-4807-a753-3d3359c34665�
�
$dabc247e-ad49-4807-a753-3d3359c34665
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   j   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n ���
�l o a d   S e r v i c e \ F i l e s \ *gnx��  9 6 0 1 9 9 . c r d o w n l o a d r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d a b c 2 4 7 e - a d 4 9 - 4 8 0 7 - a 7 5 3 - 3 d 3 3 5 9 c 3 4 6 6 5   x����֭������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ� �� � � � � ����������������������


     &�6��          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   x�֧��ѭ����֭��� 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     గ�S�          &9_32717013-f3b8-430b-9d17-8539ecd8b501�	$32717013-f3b8-430b-9d17-8539ecd8b501��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\32717013-f3b8-430b-9d17-8539ecd8b5018��ߐ�@���ߐ�HPϟ�/X�֧`���ߐ�p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8-MbIO-ZEa49EFSC0Wk4jmiiJ6knwJuZaVigkbxNZpnXW2x2qB-R4djWSHj7SPYN-Cg71bwzuC3MXaXEw accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�;���          021_download,32717013-f3b8-430b-9d17-8539ecd8b501�
�
$32717013-f3b8-430b-9d17-8539ecd8b501
�����؀��"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 3 2 7 1 7 0 1 3 - f 3 b 8 - 4 3 0 b - 9 d 1 7 - 8 5 3 9 e c d 8 b 5 0 1   x�֧��ѭ����֭��� 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     -�> �           021_download,32717013-f3b8-430b-9d17-8539ecd8b501ųZ���          021_download,dabc247e-ad49-4807-a753-3d3359c34665�
�
$dabc247e-ad49-4807-a753-3d3359c34665
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d a b c 2 4 7 e - a d 4 9 - 4 8 0 7 - a 7 5 3 - 3 d 3 3 5 9 c 3 4 6 6 5   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d a b c 2 4 7 e - a d 4 9 - 4 8 0 7 - a 7 5 3 - 3 d 3 3 5 9 c 3 4 6 6 5   x����֭����֭��� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     	�����          &9_dabc247e-ad49-4807-a753-3d3359c34665�	$dabc247e-ad49-4807-a753-3d3359c34665��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2yC:\Users\<USER>\PyCharmMiscProject\chrome_profile_3545\Default\Download Service\Files\dabc247e-ad49-4807-a753-3d3359c346658��ߐ�@���ߐ�HPϟ�/X��`���ߐ�p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88LHFOyOC08TBFDGR4X498MjgV3vVDBN9WDyZi39lF4gAICb9M1Yvty3hObWk-nyYrPTCQq_9qIKnSg1A vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 03:50:57 GMT expires:Wed, 16 Jul 2025 03:50:57 GMT accept-ranges:bytes x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�aF��          021_download,dabc247e-ad49-4807-a753-3d3359c34665�
�
$dabc247e-ad49-4807-a753-3d3359c34665
�����؀��"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d a b c 2 4 7 e - a d 4 9 - 4 8 0 7 - a 7 5 3 - 3 d 3 3 5 9 c 3 4 6 6 5   r��   y   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ 3 5 4 5 \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ d a b c 2 4 7 e - a d 4 9 - 4 8 0 7 - a 7 5 3 - 3 d 3 3 5 9 c 3 4 6 6 5   x����֭����֭��� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     �J]> �           021_download,dabc247e-ad49-4807-a753-3d3359c34665�~�F�          	39_config�
��؈��O�ԓ ��1
����Ą���ԓ ��1
�����ٝ���ԓ ��1
�����ؿ���ԓ ��1
�ހ���`�ԓ ��1
"�������d�ԓ ��1(���ʖ����
ۯ��Њ���ԓ ��1
��՛�����ԓ ��1
���Åօ�C�ԓ ��1
�����Ӆ���ԓ ��1
��������_�ԓ ��1
�����������I ��1
�������I ��1
ʒ���қ�C��I ��1
���޾���,��I ��1
���������I ��1
������t�ԓ ��1
��������k�ԓ ��1
գ��������ԓ ��1
��ר�ٳ���ԓ ��1
ෛ�������ԓ ��1
������Ʉ��ԓ ��1
"��ї�Z�ԓ ��1(Ȏ�������
#�򖐩�����ԓ ��1(Ȏ�������
#�ɕԺ����ԓ ��1(Ȏ�������
!������վN�ԓ ��1(��������'
"��������ԓ ��1(��������'
"��ڀ����ԓ ��1(��������'
!���䍟��B�ԓ ��1(��������'
"����̂呮�ԓ ��1(��������'
#������Ơ��ԓ ��1(��袺ص��
#�풠�����ԓ ��1(��袺ص��
!�����Ù��ԓ ��1(�ٴ�ڥ�7
!���������ԓ ��1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ ��1(�ٴ�ڥ�7
!������ڷu�ԓ ��1(�ٴ�ڥ�7���'d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(��10�Ұ�; �          #38_h       OG�A<T   �a-   �a-	
̃�l�}Y�9 �          #38_h       k�l��,�   �a/   �a/
 �l