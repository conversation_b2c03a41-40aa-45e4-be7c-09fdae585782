"""
Optimized Cloudflare Bypass Module
Realistic browser simulation for bypassing Cloudflare protection
"""

import time
import random
import logging
import os
from typing import Optional, Dict, List
from DrissionPage import ChromiumPage, ChromiumOptions, Chromium


class OptimizedCloudflareBypass:
    """Optimized Cloudflare bypass with realistic browser simulation"""
    
    def __init__(self):
        self.logger = logging.getLogger("cloudflare_bypass_optimized")
        
        # 真实的用户代理（最新版本）
        self.user_agents = [
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0"
        ]
        
        # 常见的屏幕分辨率
        self.viewports = [
            (1920, 1080), (1366, 768), (1536, 864), (1440, 900)
        ]
    
    def create_realistic_options(self) -> ChromiumOptions:
        """创建尽可能真实的浏览器选项"""
        options = ChromiumOptions()
        
        # 关键：使用有头模式
        options.headless(False)
        
        # 最小化的反检测设置 - 只保留必要的
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-blink-features=AutomationControlled')
        options.set_argument('--exclude-switches=enable-automation')
        
        # 保持所有真实浏览器功能
        # 不禁用 JavaScript - Cloudflare 需要
        # 不禁用图片 - 验证码需要
        # 不禁用插件 - 保持真实性
        
        # 随机窗口大小
        viewport = random.choice(self.viewports)
        options.set_argument(f'--window-size={viewport[0]},{viewport[1]}')
        
        # 真实用户代理
        user_agent = random.choice(self.user_agents)
        options.set_user_agent(user_agent)
        
        # 语言设置
        options.set_argument('--lang=zh-CN,zh;q=0.9,en;q=0.8')
        
        # 创建用户数据目录（模拟真实用户）
        user_data_dir = os.path.join(os.getcwd(), 'chrome_user_data')
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')
        
        # 启用一些真实浏览器特征
        options.set_argument('--enable-features=NetworkService')
        options.set_argument('--disable-features=VizDisplayCompositor')
        
        return options
    
    def create_realistic_browser(self) -> Chromium:
        """创建真实的浏览器实例"""
        options = self.create_realistic_options()
        
        try:
            browser = Chromium(addr_or_opts=options)
            self.logger.info("Created realistic browser instance")
            return browser
        except Exception as e:
            self.logger.error(f"Failed to create browser: {e}")
            raise
    
    def create_realistic_page(self) -> ChromiumPage:
        """创建真实的页面实例"""
        options = self.create_realistic_options()
        
        try:
            page = ChromiumPage(addr_or_opts=options)
            
            # 注入最小化的反检测脚本
            self._inject_minimal_stealth(page)
            
            self.logger.info("Created realistic page instance")
            return page
        except Exception as e:
            self.logger.error(f"Failed to create page: {e}")
            raise
    
    def _inject_minimal_stealth(self, page: ChromiumPage):
        """注入最小化的反检测脚本"""
        try:
            # 只隐藏最明显的自动化特征
            stealth_script = """
            // 隐藏 webdriver 属性
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 确保 chrome 对象存在
            if (!window.chrome) {
                window.chrome = {
                    runtime: {},
                };
            }
            """
            
            page.run_js(stealth_script)
            self.logger.debug("Minimal stealth scripts injected")
            
        except Exception as e:
            self.logger.warning(f"Failed to inject stealth scripts: {e}")
    
    def navigate_safely(self, page: ChromiumPage, url: str, max_retries: int = 2) -> bool:
        """安全导航到 URL"""
        for attempt in range(max_retries):
            try:
                self.logger.info(f"Navigating to {url} (attempt {attempt + 1})")
                
                # 随机延迟模拟人类行为
                time.sleep(random.uniform(1, 3))
                
                # 导航
                page.get(url)
                
                # 等待页面加载
                time.sleep(random.uniform(3, 5))
                
                # 检查是否成功
                if self._check_page_loaded(page):
                    # 如果遇到 Cloudflare，等待更长时间
                    if self._is_cloudflare_page(page):
                        self.logger.info("Cloudflare detected, waiting for resolution...")
                        return self._wait_for_cloudflare_resolution(page)
                    else:
                        self.logger.info("Page loaded successfully")
                        return True
                else:
                    self.logger.warning(f"Page load failed on attempt {attempt + 1}")
                    
            except Exception as e:
                self.logger.error(f"Navigation attempt {attempt + 1} failed: {e}")
                time.sleep(random.uniform(3, 6))
        
        return False
    
    def _check_page_loaded(self, page: ChromiumPage) -> bool:
        """检查页面是否加载完成"""
        try:
            # 检查基本的页面元素
            title = page.title
            if title and len(title) > 0:
                return True
            return False
        except:
            return False
    
    def _is_cloudflare_page(self, page: ChromiumPage) -> bool:
        """检查是否是 Cloudflare 页面"""
        try:
            title = page.title.lower()
            page_text = page.html.lower()
            
            cloudflare_indicators = [
                '请稍候', 'just a moment', 'checking your browser',
                'cloudflare', 'cf-ray', 'ddos protection'
            ]
            
            for indicator in cloudflare_indicators:
                if indicator in title or indicator in page_text:
                    return True
            
            return False
        except:
            return False
    
    def _wait_for_cloudflare_resolution(self, page: ChromiumPage, timeout: int = 60) -> bool:
        """等待 Cloudflare 自动解决"""
        self.logger.info("Waiting for Cloudflare challenge to resolve...")
        
        start_time = time.time()
        check_interval = 3
        
        while time.time() - start_time < timeout:
            try:
                # 模拟轻微的人类活动
                if random.random() < 0.3:  # 30% 概率
                    self._simulate_light_activity(page)
                
                # 检查是否还在 Cloudflare 页面
                if not self._is_cloudflare_page(page):
                    self.logger.info("✅ Cloudflare challenge resolved!")
                    time.sleep(2)  # 额外等待确保页面稳定
                    return True
                
                time.sleep(check_interval)
                
            except Exception as e:
                self.logger.debug(f"Error during Cloudflare wait: {e}")
                time.sleep(check_interval)
        
        self.logger.warning("⚠️ Cloudflare challenge timeout")
        return False
    
    def _simulate_light_activity(self, page: ChromiumPage):
        """模拟轻微的人类活动"""
        try:
            # 随机滚动
            if random.random() < 0.5:
                scroll_amount = random.randint(50, 200)
                page.scroll.down(scroll_amount)
                time.sleep(random.uniform(0.5, 1))
            
            # 随机鼠标移动
            if random.random() < 0.3:
                x = random.randint(100, 500)
                y = random.randint(100, 400)
                try:
                    page.actions.move_to((x, y))
                except:
                    pass
                
        except Exception as e:
            self.logger.debug(f"Error simulating activity: {e}")


def create_optimized_browser() -> Chromium:
    """创建优化的浏览器实例"""
    bypass = OptimizedCloudflareBypass()
    return bypass.create_realistic_browser()


def create_optimized_page() -> ChromiumPage:
    """创建优化的页面实例"""
    bypass = OptimizedCloudflareBypass()
    return bypass.create_realistic_page()


def navigate_with_optimization(page: ChromiumPage, url: str) -> bool:
    """使用优化策略导航"""
    bypass = OptimizedCloudflareBypass()
    return bypass.navigate_safely(page, url)
