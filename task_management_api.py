"""
Task Management API for Web Crawling System
Provides REST API endpoints for managing forum post crawling tasks
"""

import sqlite3
import uuid
import threading
import time
from datetime import datetime, timezone
from typing import List, Optional, Dict, Any
from enum import Enum

from fastapi import FastAPI, HTTPException, BackgroundTasks
from pydantic import BaseModel, Field
import uvicorn

try:
    from improved_forum_crawler import <PERSON>rawlerConfig, ForumCrawler
except ImportError:
    print("Warning: improved_forum_crawler not found, using basic config")
    class CrawlerConfig:
        def __init__(self):
            self.flash_sale_keywords = ["offer", "sale", "discount"]
        def load_from_file(self, path):
            pass

from task_browser_manager import TaskBrowserManager
from task_execution_engine import TaskExecutionEngine


class TaskStatus(str, Enum):
    PENDING = "pending"
    RUNNING = "running" 
    STOPPED = "stopped"
    COMPLETED = "completed"
    ERROR = "error"


class TaskBase(BaseModel):
    post_url: str = Field(..., description="URL of the forum post to crawl")
    forum_domain: str = Field(..., description="Domain of the forum (e.g., lowendtalk.com)")
    monitor_interval: int = Field(default=45, description="Monitoring interval in seconds")
    ai_analysis_enabled: bool = Field(default=True, description="Enable AI analysis for flash sales")


class TaskCreate(TaskBase):
    pass


class TaskUpdate(BaseModel):
    monitor_interval: Optional[int] = None
    ai_analysis_enabled: Optional[bool] = None


class TaskInDB(TaskBase):
    id: str
    status: TaskStatus
    created_at: datetime
    started_at: Optional[datetime] = None
    stopped_at: Optional[datetime] = None
    last_check_at: Optional[datetime] = None
    comment_count: int = 0
    flash_sales_found: int = 0
    error_message: Optional[str] = None


class TaskManager:
    """Manages task persistence and lifecycle"""
    
    def __init__(self, db_path: str = "crawling_tasks.db"):
        self.db_path = db_path
        self.lock = threading.Lock()
        self._init_db()
    
    def _init_db(self):
        """Initialize the tasks database"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS tasks (
                    id TEXT PRIMARY KEY,
                    post_url TEXT UNIQUE NOT NULL,
                    forum_domain TEXT NOT NULL,
                    monitor_interval INTEGER NOT NULL,
                    ai_analysis_enabled BOOLEAN NOT NULL,
                    status TEXT NOT NULL,
                    created_at TEXT NOT NULL,
                    started_at TEXT,
                    stopped_at TEXT,
                    last_check_at TEXT,
                    comment_count INTEGER DEFAULT 0,
                    flash_sales_found INTEGER DEFAULT 0,
                    error_message TEXT
                )
            """)
            conn.commit()
    
    def create_task(self, task_data: TaskCreate) -> TaskInDB:
        """Create a new crawling task"""
        with self.lock:
            task_id = str(uuid.uuid4())
            now = datetime.now(timezone.utc)
            
            with sqlite3.connect(self.db_path) as conn:
                try:
                    conn.execute("""
                        INSERT INTO tasks (
                            id, post_url, forum_domain, monitor_interval, 
                            ai_analysis_enabled, status, created_at
                        ) VALUES (?, ?, ?, ?, ?, ?, ?)
                    """, (
                        task_id, task_data.post_url, task_data.forum_domain,
                        task_data.monitor_interval, task_data.ai_analysis_enabled,
                        TaskStatus.PENDING, now.isoformat()
                    ))
                    conn.commit()
                    
                    return self.get_task(task_id)
                    
                except sqlite3.IntegrityError:
                    raise HTTPException(
                        status_code=400, 
                        detail=f"Task for post URL {task_data.post_url} already exists"
                    )
    
    def get_task(self, task_id: str) -> TaskInDB:
        """Get a task by ID"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM tasks WHERE id = ?", (task_id,))
            row = cursor.fetchone()
            
            if not row:
                raise HTTPException(status_code=404, detail="Task not found")
            
            return TaskInDB(**dict(row))
    
    def get_all_tasks(self) -> List[TaskInDB]:
        """Get all tasks"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute("SELECT * FROM tasks ORDER BY created_at DESC")
            rows = cursor.fetchall()
            
            return [TaskInDB(**dict(row)) for row in rows]
    
    def update_task_status(self, task_id: str, status: TaskStatus, 
                          error_message: Optional[str] = None):
        """Update task status"""
        with self.lock:
            now = datetime.now(timezone.utc).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                if status == TaskStatus.RUNNING:
                    conn.execute("""
                        UPDATE tasks SET status = ?, started_at = ?, error_message = NULL 
                        WHERE id = ?
                    """, (status, now, task_id))
                elif status in [TaskStatus.STOPPED, TaskStatus.COMPLETED, TaskStatus.ERROR]:
                    conn.execute("""
                        UPDATE tasks SET status = ?, stopped_at = ?, error_message = ? 
                        WHERE id = ?
                    """, (status, now, error_message, task_id))
                else:
                    conn.execute("""
                        UPDATE tasks SET status = ?, error_message = ? WHERE id = ?
                    """, (status, error_message, task_id))
                
                conn.commit()
    
    def update_task_stats(self, task_id: str, comment_count: int, flash_sales_found: int):
        """Update task statistics"""
        with self.lock:
            now = datetime.now(timezone.utc).isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                conn.execute("""
                    UPDATE tasks SET comment_count = ?, flash_sales_found = ?, last_check_at = ?
                    WHERE id = ?
                """, (comment_count, flash_sales_found, now, task_id))
                conn.commit()
    
    def delete_task(self, task_id: str):
        """Delete a task"""
        with self.lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute("DELETE FROM tasks WHERE id = ?", (task_id,))
                if cursor.rowcount == 0:
                    raise HTTPException(status_code=404, detail="Task not found")
                conn.commit()
    
    def get_active_tasks(self) -> List[TaskInDB]:
        """Get all active (running) tasks"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute(
                "SELECT * FROM tasks WHERE status = ? ORDER BY created_at", 
                (TaskStatus.RUNNING,)
            )
            rows = cursor.fetchall()
            
            return [TaskInDB(**dict(row)) for row in rows]


# Global instances
task_manager = TaskManager()
execution_engine = None  # Will be initialized in startup


app = FastAPI(
    title="Web Crawling Task Management API",
    description="REST API for managing forum post crawling tasks with real-time monitoring",
    version="1.0.0"
)


@app.on_event("startup")
async def startup_event():
    """Initialize the execution engine on startup"""
    global execution_engine
    
    # Load crawler configuration
    config = CrawlerConfig()
    config.load_from_file("crawler_config.json")
    
    # Initialize execution engine
    execution_engine = TaskExecutionEngine(config, task_manager)
    
    print("🚀 Task Management API started successfully")


@app.on_event("shutdown") 
async def shutdown_event():
    """Cleanup on shutdown"""
    global execution_engine
    
    if execution_engine:
        await execution_engine.stop_all_tasks()
        execution_engine.cleanup()
    
    print("🛑 Task Management API shutdown complete")


# API Endpoints

@app.get("/")
async def read_root():
    """API health check"""
    return {
        "message": "Web Crawling Task Management API",
        "status": "running",
        "active_tasks": len(task_manager.get_active_tasks())
    }


@app.post("/tasks", response_model=TaskInDB, summary="Create new crawling task")
async def create_task(task: TaskCreate):
    """Create a new forum post crawling task"""
    return task_manager.create_task(task)


@app.get("/tasks", response_model=List[TaskInDB], summary="List all tasks")
async def list_tasks():
    """Get all crawling tasks"""
    return task_manager.get_all_tasks()


@app.get("/tasks/{task_id}", response_model=TaskInDB, summary="Get specific task")
async def get_task(task_id: str):
    """Get a specific task by ID"""
    return task_manager.get_task(task_id)


@app.put("/tasks/{task_id}/start", summary="Start a specific task")
async def start_task(task_id: str, background_tasks: BackgroundTasks):
    """Start monitoring a specific crawling task"""
    task = task_manager.get_task(task_id)
    
    if task.status == TaskStatus.RUNNING:
        raise HTTPException(status_code=400, detail="Task is already running")
    
    # Start task in background
    background_tasks.add_task(execution_engine.start_task, task_id)
    
    return {"message": f"Task {task_id} started successfully"}


@app.put("/tasks/{task_id}/stop", summary="Stop a specific task")
async def stop_task(task_id: str):
    """Stop a specific crawling task"""
    task = task_manager.get_task(task_id)
    
    if task.status != TaskStatus.RUNNING:
        raise HTTPException(status_code=400, detail="Task is not running")
    
    await execution_engine.stop_task(task_id)
    
    return {"message": f"Task {task_id} stopped successfully"}


@app.delete("/tasks/{task_id}", summary="Delete a specific task")
async def delete_task(task_id: str):
    """Delete a crawling task"""
    task = task_manager.get_task(task_id)
    
    if task.status == TaskStatus.RUNNING:
        await execution_engine.stop_task(task_id)
    
    task_manager.delete_task(task_id)
    
    return {"message": f"Task {task_id} deleted successfully"}


@app.post("/tasks/stop-all", summary="Stop all running tasks")
async def stop_all_tasks():
    """Stop all currently running tasks"""
    active_tasks = task_manager.get_active_tasks()
    
    if not active_tasks:
        return {"message": "No active tasks to stop"}
    
    await execution_engine.stop_all_tasks()
    
    return {
        "message": f"Stopped {len(active_tasks)} tasks successfully",
        "stopped_tasks": [task.id for task in active_tasks]
    }


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
