#!/usr/bin/env python3
"""
Test script for Redis distributed locking functionality
"""

import sys
import os
import time
import threading
import json
from concurrent.futures import ThreadPoolExecutor

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import CrawlerConfig, StateManager, RedisLockManager


def test_redis_availability():
    """Test if Redis is available and can be connected to"""
    print("Testing Redis availability...")
    
    try:
        import redis
        print("✓ Redis package is available")
        
        # Test connection to default Redis instance
        try:
            r = redis.Redis(host='localhost', port=6379, db=0, socket_connect_timeout=2)
            r.ping()
            print("✓ Redis server is running and accessible")
            return True
        except Exception as e:
            print(f"✗ Redis server not accessible: {e}")
            return False
            
    except ImportError:
        print("✗ Redis package not installed")
        return False


def test_fallback_locking():
    """Test fallback to file-based locking when <PERSON><PERSON> is disabled"""
    print("\nTesting fallback locking mechanism...")
    
    # Create config with Redis disabled
    config = CrawlerConfig()
    config.redis_enabled = False
    
    state_manager = StateManager(config)
    
    # Test that it uses fallback locking
    assert not state_manager.lock_manager.redis_available, "Should use fallback when Redis disabled"
    
    # Test basic operations
    test_state = {"test": "data", "processed_posts": {}}
    
    try:
        state_manager.save_state(test_state)
        loaded_state = state_manager.load_state()
        
        assert "test" in loaded_state, "State should be saved and loaded correctly"
        print("✓ Fallback locking works correctly")
        return True
        
    except Exception as e:
        print(f"✗ Fallback locking failed: {e}")
        return False


def test_redis_locking_if_available():
    """Test Redis locking if Redis is available"""
    print("\nTesting Redis distributed locking...")
    
    if not test_redis_availability():
        print("⚠️  Skipping Redis locking tests (Redis not available)")
        return True
    
    # Create config with Redis enabled
    config = CrawlerConfig()
    config.redis_enabled = True
    config.redis_host = "localhost"
    config.redis_port = 6379
    config.redis_db = 0
    
    try:
        state_manager = StateManager(config)
        
        if state_manager.lock_manager.redis_available:
            print("✓ Redis connection established")
            
            # Test basic operations with Redis locking
            test_state = {"test": "redis_data", "processed_posts": {}}
            
            state_manager.save_state(test_state)
            loaded_state = state_manager.load_state()
            
            assert "test" in loaded_state, "State should be saved and loaded with Redis locking"
            print("✓ Redis locking works correctly")
            return True
        else:
            print("⚠️  Redis locking fell back to file-based locking")
            return True
            
    except Exception as e:
        print(f"✗ Redis locking test failed: {e}")
        return False


def test_concurrent_access():
    """Test concurrent access with locking"""
    print("\nTesting concurrent access protection...")
    
    config = CrawlerConfig()
    config.redis_enabled = False  # Use fallback for consistent testing
    
    results = []
    errors = []
    
    def worker_function(worker_id):
        """Worker function that modifies shared state"""
        try:
            state_manager = StateManager(config)
            
            # Load current state
            current_state = state_manager.load_state()
            
            # Simulate some processing time
            time.sleep(0.1)
            
            # Modify state
            if "counter" not in current_state:
                current_state["counter"] = 0
            current_state["counter"] += 1
            current_state[f"worker_{worker_id}"] = f"processed_at_{time.time()}"
            
            # Save modified state
            state_manager.save_state(current_state)
            
            results.append(f"Worker {worker_id} completed")
            
        except Exception as e:
            errors.append(f"Worker {worker_id} error: {e}")
    
    # Run multiple workers concurrently
    num_workers = 5
    with ThreadPoolExecutor(max_workers=num_workers) as executor:
        futures = [executor.submit(worker_function, i) for i in range(num_workers)]
        
        # Wait for all workers to complete
        for future in futures:
            future.result()
    
    # Check results
    if errors:
        print(f"✗ Concurrent access test failed with errors: {errors}")
        return False
    
    # Verify final state
    final_state_manager = StateManager(config)
    final_state = final_state_manager.load_state()
    
    expected_counter = num_workers
    actual_counter = final_state.get("counter", 0)
    
    if actual_counter == expected_counter:
        print(f"✓ Concurrent access test passed (counter: {actual_counter}/{expected_counter})")
        return True
    else:
        print(f"✗ Concurrent access test failed (counter: {actual_counter}/{expected_counter})")
        return False


def test_lock_timeout():
    """Test lock timeout functionality"""
    print("\nTesting lock timeout functionality...")
    
    config = CrawlerConfig()
    config.redis_enabled = False
    config.redis_lock_timeout = 2  # Short timeout for testing
    
    lock_manager = RedisLockManager(config)
    
    def long_running_task():
        """Task that holds lock for a long time"""
        with lock_manager.acquire_lock("test_timeout"):
            time.sleep(5)  # Hold lock longer than timeout
    
    def quick_task():
        """Task that tries to acquire the same lock"""
        try:
            with lock_manager.acquire_lock("test_timeout"):
                return "acquired"
        except Exception as e:
            return f"failed: {e}"
    
    # Start long running task
    thread1 = threading.Thread(target=long_running_task)
    thread1.start()
    
    # Wait a bit, then try to acquire same lock
    time.sleep(0.5)
    result = quick_task()
    
    # Wait for first thread to complete
    thread1.join()
    
    # For file-based locking, the second task should wait and eventually succeed
    # This test mainly verifies the timeout mechanism exists
    print(f"✓ Lock timeout test completed (result: {result})")
    return True


def main():
    """Run all Redis locking tests"""
    print("Redis Distributed Locking Test Suite")
    print("=" * 50)
    
    tests = [
        test_redis_availability,
        test_fallback_locking,
        test_redis_locking_if_available,
        test_concurrent_access,
        test_lock_timeout
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                print(f"❌ {test.__name__} FAILED")
        except Exception as e:
            print(f"❌ {test.__name__} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Redis locking tests PASSED!")
        print("\nRedis distributed locking is ready for use!")
        print("\nTo enable Redis locking:")
        print('1. Install Redis: pip install redis')
        print('2. Start Redis server: redis-server')
        print('3. Set "redis_enabled": true in crawler_config.json')
    else:
        print("⚠️  Some tests failed. Please check the issues above.")
    
    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
