#!/usr/bin/env python3
"""
Test Script for Web Crawling Task Management System
Validates system functionality and API endpoints
"""

import asyncio
import json
import time
import requests
import threading
from typing import Dict, List

# Test configuration
API_BASE_URL = "http://localhost:8000"
TEST_TIMEOUT = 30


class TaskSystemTester:
    """Test suite for the task management system"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.session = requests.Session()
        self.created_tasks: List[str] = []
        
    def test_api_health(self) -> bool:
        """Test API health check endpoint"""
        print("🔍 Testing API health check...")
        
        try:
            response = self.session.get(f"{self.base_url}/")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API is healthy: {data.get('message', 'Unknown')}")
                print(f"   Active tasks: {data.get('active_tasks', 0)}")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ API health check error: {e}")
            return False
    
    def test_create_task(self) -> str:
        """Test task creation"""
        print("🔍 Testing task creation...")
        
        task_data = {
            "post_url": "https://lowendtalk.com/discussion/test-post",
            "forum_domain": "lowendtalk.com",
            "monitor_interval": 60,
            "ai_analysis_enabled": True
        }
        
        try:
            response = self.session.post(
                f"{self.base_url}/tasks",
                json=task_data
            )
            
            if response.status_code == 200:
                task = response.json()
                task_id = task["id"]
                self.created_tasks.append(task_id)
                
                print(f"✅ Task created successfully: {task_id}")
                print(f"   Post URL: {task['post_url']}")
                print(f"   Status: {task['status']}")
                return task_id
            else:
                print(f"❌ Task creation failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Task creation error: {e}")
            return None
    
    def test_list_tasks(self) -> bool:
        """Test task listing"""
        print("🔍 Testing task listing...")
        
        try:
            response = self.session.get(f"{self.base_url}/tasks")
            
            if response.status_code == 200:
                tasks = response.json()
                print(f"✅ Retrieved {len(tasks)} tasks")
                
                for task in tasks[:3]:  # Show first 3 tasks
                    print(f"   Task {task['id'][:8]}: {task['status']} - {task['post_url']}")
                
                return True
            else:
                print(f"❌ Task listing failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Task listing error: {e}")
            return False
    
    def test_get_task(self, task_id: str) -> bool:
        """Test getting specific task"""
        print(f"🔍 Testing get task: {task_id[:8]}...")
        
        try:
            response = self.session.get(f"{self.base_url}/tasks/{task_id}")
            
            if response.status_code == 200:
                task = response.json()
                print(f"✅ Retrieved task details")
                print(f"   ID: {task['id'][:8]}")
                print(f"   Status: {task['status']}")
                print(f"   Created: {task['created_at']}")
                print(f"   Comments: {task['comment_count']}")
                return True
            else:
                print(f"❌ Get task failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Get task error: {e}")
            return False
    
    def test_start_task(self, task_id: str) -> bool:
        """Test starting a task"""
        print(f"🔍 Testing start task: {task_id[:8]}...")
        
        try:
            response = self.session.put(f"{self.base_url}/tasks/{task_id}/start")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Task started: {result.get('message', 'Success')}")
                return True
            else:
                print(f"❌ Start task failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Start task error: {e}")
            return False
    
    def test_stop_task(self, task_id: str) -> bool:
        """Test stopping a task"""
        print(f"🔍 Testing stop task: {task_id[:8]}...")
        
        try:
            response = self.session.put(f"{self.base_url}/tasks/{task_id}/stop")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Task stopped: {result.get('message', 'Success')}")
                return True
            else:
                print(f"❌ Stop task failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except Exception as e:
            print(f"❌ Stop task error: {e}")
            return False
    
    def test_delete_task(self, task_id: str) -> bool:
        """Test deleting a task"""
        print(f"🔍 Testing delete task: {task_id[:8]}...")
        
        try:
            response = self.session.delete(f"{self.base_url}/tasks/{task_id}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ Task deleted: {result.get('message', 'Success')}")
                return True
            else:
                print(f"❌ Delete task failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Delete task error: {e}")
            return False
    
    def test_stop_all_tasks(self) -> bool:
        """Test stopping all tasks"""
        print("🔍 Testing stop all tasks...")
        
        try:
            response = self.session.post(f"{self.base_url}/tasks/stop-all")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ All tasks stopped: {result.get('message', 'Success')}")
                if 'stopped_tasks' in result:
                    print(f"   Stopped {len(result['stopped_tasks'])} tasks")
                return True
            else:
                print(f"❌ Stop all tasks failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Stop all tasks error: {e}")
            return False
    
    def test_task_lifecycle(self) -> bool:
        """Test complete task lifecycle"""
        print("\n" + "="*60)
        print("🔄 Testing complete task lifecycle...")
        print("="*60)
        
        # Create task
        task_id = self.test_create_task()
        if not task_id:
            return False
        
        time.sleep(1)
        
        # Get task details
        if not self.test_get_task(task_id):
            return False
        
        time.sleep(1)
        
        # Start task
        if not self.test_start_task(task_id):
            return False
        
        # Wait a bit for task to run
        print("⏳ Waiting 10 seconds for task to run...")
        time.sleep(10)
        
        # Check task status
        if not self.test_get_task(task_id):
            return False
        
        # Stop task
        if not self.test_stop_task(task_id):
            return False
        
        time.sleep(1)
        
        # Delete task
        if not self.test_delete_task(task_id):
            return False
        
        print("✅ Task lifecycle test completed successfully!")
        return True
    
    def test_concurrent_tasks(self) -> bool:
        """Test creating and managing multiple concurrent tasks"""
        print("\n" + "="*60)
        print("🔄 Testing concurrent task management...")
        print("="*60)
        
        # Create multiple tasks
        task_ids = []
        for i in range(3):
            task_data = {
                "post_url": f"https://lowendtalk.com/discussion/test-post-{i}",
                "forum_domain": "lowendtalk.com",
                "monitor_interval": 120,
                "ai_analysis_enabled": True
            }
            
            try:
                response = self.session.post(f"{self.base_url}/tasks", json=task_data)
                if response.status_code == 200:
                    task = response.json()
                    task_ids.append(task["id"])
                    print(f"✅ Created task {i+1}: {task['id'][:8]}")
                else:
                    print(f"❌ Failed to create task {i+1}")
                    
            except Exception as e:
                print(f"❌ Error creating task {i+1}: {e}")
        
        if not task_ids:
            print("❌ No tasks created for concurrent test")
            return False
        
        # Start all tasks
        for task_id in task_ids:
            self.test_start_task(task_id)
            time.sleep(1)
        
        # Wait for tasks to run
        print("⏳ Waiting 15 seconds for tasks to run...")
        time.sleep(15)
        
        # Stop all tasks
        self.test_stop_all_tasks()
        
        # Clean up - delete all test tasks
        for task_id in task_ids:
            self.test_delete_task(task_id)
            time.sleep(0.5)
        
        print("✅ Concurrent task test completed!")
        return True
    
    def cleanup(self):
        """Clean up any remaining test tasks"""
        print("\n🧹 Cleaning up test tasks...")
        
        for task_id in self.created_tasks:
            try:
                # Try to stop and delete the task
                self.session.put(f"{self.base_url}/tasks/{task_id}/stop")
                self.session.delete(f"{self.base_url}/tasks/{task_id}")
                print(f"   Cleaned up task: {task_id[:8]}")
            except:
                pass  # Ignore cleanup errors
    
    def run_all_tests(self) -> bool:
        """Run all tests"""
        print("\n" + "="*80)
        print("🧪 STARTING WEB CRAWLING TASK SYSTEM TESTS")
        print("="*80)
        
        tests_passed = 0
        total_tests = 0
        
        # Individual API tests
        test_functions = [
            ("API Health Check", self.test_api_health),
            ("Task Creation", lambda: self.test_create_task() is not None),
            ("Task Listing", self.test_list_tasks),
        ]
        
        for test_name, test_func in test_functions:
            total_tests += 1
            print(f"\n📋 Running: {test_name}")
            try:
                if test_func():
                    tests_passed += 1
                    print(f"✅ {test_name} PASSED")
                else:
                    print(f"❌ {test_name} FAILED")
            except Exception as e:
                print(f"❌ {test_name} ERROR: {e}")
        
        # Lifecycle test
        total_tests += 1
        print(f"\n📋 Running: Task Lifecycle Test")
        try:
            if self.test_task_lifecycle():
                tests_passed += 1
                print(f"✅ Task Lifecycle Test PASSED")
            else:
                print(f"❌ Task Lifecycle Test FAILED")
        except Exception as e:
            print(f"❌ Task Lifecycle Test ERROR: {e}")
        
        # Concurrent tasks test
        total_tests += 1
        print(f"\n📋 Running: Concurrent Tasks Test")
        try:
            if self.test_concurrent_tasks():
                tests_passed += 1
                print(f"✅ Concurrent Tasks Test PASSED")
            else:
                print(f"❌ Concurrent Tasks Test FAILED")
        except Exception as e:
            print(f"❌ Concurrent Tasks Test ERROR: {e}")
        
        # Cleanup
        self.cleanup()
        
        # Results
        print("\n" + "="*80)
        print("📊 TEST RESULTS")
        print("="*80)
        print(f"Tests Passed: {tests_passed}/{total_tests}")
        print(f"Success Rate: {(tests_passed/total_tests)*100:.1f}%")
        
        if tests_passed == total_tests:
            print("🎉 ALL TESTS PASSED! System is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Check the output above for details.")
            return False


def main():
    """Main test function"""
    import argparse
    
    parser = argparse.ArgumentParser(description='Test Web Crawling Task Management System')
    parser.add_argument('--url', default=API_BASE_URL, help='API base URL')
    parser.add_argument('--timeout', type=int, default=TEST_TIMEOUT, help='Test timeout in seconds')
    
    args = parser.parse_args()
    
    print("🧪 Web Crawling Task Management System - Test Suite")
    print(f"🌐 Testing API at: {args.url}")
    print(f"⏱️  Timeout: {args.timeout} seconds")
    
    tester = TaskSystemTester(args.url)
    
    try:
        success = tester.run_all_tests()
        exit_code = 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
        tester.cleanup()
        exit_code = 130
    except Exception as e:
        print(f"\n❌ Test suite error: {e}")
        tester.cleanup()
        exit_code = 1
    
    exit(exit_code)


if __name__ == "__main__":
    main()
