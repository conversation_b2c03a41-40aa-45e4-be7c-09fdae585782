#!/usr/bin/env python3
"""
Aggressive Cloudflare Bypass
更激进的反检测策略
"""

import time
import random
import sys
import logging
import os
from DrissionPage import ChromiumPage, ChromiumOptions

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AggressiveCloudflareBypass:
    """更激进的 Cloudflare 绕过策略"""
    
    def __init__(self):
        self.logger = logging.getLogger("aggressive_cf")
        
        # 经过验证的真实用户代理（来自真实浏览器）
        self.proven_user_agents = [
            # Chrome 117 - 较老但稳定的版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36",
            # Chrome 116 - 更老的版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
            # Chrome 115 - 经典稳定版本
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36",
            # Firefox 115 ESR - 企业版，通常检测较松
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/115.0",
            # Edge 基于 Chromium 但检测可能不同
            "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/115.0.0.0 Safari/537.36 Edg/115.0.1901.203"
        ]
    
    def create_aggressive_options(self, user_agent: str = None) -> ChromiumOptions:
        """创建激进的反检测选项"""
        options = ChromiumOptions()
        
        # 使用有头模式 - 关键！
        options.headless(False)
        
        # 选择用户代理
        if not user_agent:
            user_agent = random.choice(self.proven_user_agents)
        
        options.set_user_agent(user_agent)
        
        # 最小化的参数 - 只保留绝对必要的
        options.set_argument('--no-sandbox')
        options.set_argument('--disable-blink-features=AutomationControlled')
        
        # 窗口设置
        options.set_argument('--window-size=1366,768')  # 常见分辨率
        
        # 语言设置
        options.set_argument('--lang=zh-CN,zh;q=0.9,en;q=0.8')
        
        # 创建独立的用户数据目录
        user_data_dir = os.path.join(os.getcwd(), f'chrome_profile_{random.randint(1000, 9999)}')
        if not os.path.exists(user_data_dir):
            os.makedirs(user_data_dir, exist_ok=True)
        options.set_argument(f'--user-data-dir={user_data_dir}')
        
        # 禁用一些可能暴露自动化的功能
        options.set_argument('--disable-extensions')
        options.set_argument('--disable-plugins')
        options.set_argument('--disable-default-apps')
        
        return options
    
    def create_stealth_page(self, user_agent: str = None) -> ChromiumPage:
        """创建隐身页面"""
        options = self.create_aggressive_options(user_agent)
        
        try:
            page = ChromiumPage(addr_or_opts=options)
            
            # 注入更强的反检测脚本
            self._inject_aggressive_stealth(page)
            
            return page
        except Exception as e:
            self.logger.error(f"Failed to create stealth page: {e}")
            raise
    
    def _inject_aggressive_stealth(self, page: ChromiumPage):
        """注入激进的反检测脚本"""
        try:
            stealth_script = """
            // 完全隐藏 webdriver
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
            
            // 伪造 plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [
                    {name: 'Chrome PDF Plugin', description: 'Portable Document Format', filename: 'internal-pdf-viewer'},
                    {name: 'Chrome PDF Viewer', description: '', filename: 'mhjfbmdgcfjbbpaeojofohoefgiehjai'},
                    {name: 'Native Client', description: '', filename: 'internal-nacl-plugin'}
                ],
            });
            
            // 伪造 languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['zh-CN', 'zh', 'en-US', 'en'],
            });
            
            // 确保 chrome 对象存在
            if (!window.chrome) {
                window.chrome = {
                    runtime: {},
                    loadTimes: function() {
                        return {
                            commitLoadTime: Date.now() / 1000 - Math.random(),
                            finishDocumentLoadTime: Date.now() / 1000 - Math.random(),
                            finishLoadTime: Date.now() / 1000 - Math.random(),
                            firstPaintAfterLoadTime: 0,
                            firstPaintTime: Date.now() / 1000 - Math.random(),
                            navigationType: 'Other',
                            npnNegotiatedProtocol: 'h2',
                            requestTime: Date.now() / 1000 - Math.random(),
                            startLoadTime: Date.now() / 1000 - Math.random(),
                            wasAlternateProtocolAvailable: false,
                            wasFetchedViaSpdy: true,
                            wasNpnNegotiated: true
                        };
                    },
                    csi: function() {
                        return {
                            onloadT: Date.now(),
                            pageT: Date.now() - Math.random() * 1000,
                            startE: Date.now() - Math.random() * 2000,
                            tran: 15
                        };
                    }
                };
            }
            
            // 修改 permissions API
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
            
            // 隐藏自动化相关的属性
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Array;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Promise;
            delete window.cdc_adoQpoasnfa76pfcZLmcfl_Symbol;
            """
            
            page.run_js(stealth_script)
            self.logger.debug("Aggressive stealth scripts injected")
            
        except Exception as e:
            self.logger.warning(f"Failed to inject stealth scripts: {e}")
    
    def enhanced_cloudflare_handler(self, tab: ChromiumPage) -> bool:
        """增强的 Cloudflare 处理器"""
        max_retries = 8  # 增加重试次数
        retries = 0
        
        while retries < max_retries:
            try:
                self.logger.info(f"Cloudflare 处理尝试 {retries + 1}/{max_retries}")
                
                # 检查当前页面状态
                current_title = tab.title
                self.logger.info(f"当前页面标题: {current_title}")
                
                if 'Just a moment' in current_title or '请稍候' in current_title:
                    self.logger.info("检测到 Cloudflare 挑战页面")
                    
                    # 模拟人类行为 - 随机等待
                    wait_time = random.uniform(3, 7)
                    self.logger.info(f"模拟人类等待 {wait_time:.1f} 秒...")
                    time.sleep(wait_time)
                    
                    # 随机滚动页面
                    try:
                        scroll_amount = random.randint(100, 300)
                        tab.scroll.down(scroll_amount)
                        time.sleep(random.uniform(0.5, 1.5))
                        tab.scroll.up(scroll_amount // 2)
                        time.sleep(random.uniform(0.5, 1.5))
                    except:
                        pass
                    
                    # 查找并处理 Cloudflare 元素
                    if tab.s_ele('x://div[@class="main-content"]'):
                        self.logger.info("找到 main-content 区域")
                        mainDiv = tab.ele('x://div[@class="main-content"]/div')
                        
                        # 检查输入框
                        if mainDiv.ele('x:/div/input', timeout=0.5):
                            self.logger.info("找到输入框，模拟点击")
                            input_elem = tab.s_ele('x:/div/input')
                            if input_elem:
                                # 模拟人类点击
                                time.sleep(random.uniform(0.5, 1.5))
                                input_elem.click()
                                time.sleep(random.uniform(1, 2))
                        
                        # 查找 iframe 中的复选框
                        iframe = mainDiv.ele('x:/div/div').sr('x://iframe')
                        if iframe:
                            self.logger.info("找到 iframe")
                            
                            # 等待 iframe 加载
                            time.sleep(random.uniform(2, 4))
                            
                            body = iframe.ele('x://body')
                            if body:
                                self.logger.info("找到 iframe body")
                                
                                # 查找复选框
                                checkbox = body.sr('x://input[@type="checkbox"]')
                                if checkbox:
                                    self.logger.info("找到复选框，模拟人类点击")
                                    
                                    # 模拟鼠标移动到复选框
                                    time.sleep(random.uniform(1, 2))
                                    
                                    # 点击复选框
                                    checkbox.click()
                                    
                                    # 等待验证处理
                                    verification_wait = random.uniform(8, 15)
                                    self.logger.info(f"等待验证处理 {verification_wait:.1f} 秒...")
                                    time.sleep(verification_wait)
                                    
                                    # 检查是否成功
                                    new_title = tab.title
                                    self.logger.info(f"验证后页面标题: {new_title}")
                                    
                                    if 'Just a moment' not in new_title and '请稍候' not in new_title:
                                        self.logger.info("✅ Cloudflare 挑战成功通过！")
                                        return True
                                    else:
                                        self.logger.warning("验证后仍显示挑战页面，继续尝试...")
                                        retries += 1
                                        continue
                                else:
                                    self.logger.warning("未找到复选框")
                            else:
                                self.logger.warning("未找到 iframe body")
                        else:
                            self.logger.warning("未找到 iframe")
                    else:
                        self.logger.warning("未找到 main-content")
                        
                        # 尝试等待页面自动解决
                        self.logger.info("尝试等待页面自动解决...")
                        time.sleep(10)
                        
                        # 再次检查
                        auto_title = tab.title
                        if 'Just a moment' not in auto_title and '请稍候' not in auto_title:
                            self.logger.info("✅ 页面自动解决了挑战！")
                            return True
                else:
                    self.logger.info("✅ 没有 Cloudflare 挑战，直接通过")
                    return True
                    
            except Exception as e:
                exc_type, exc_value, exc_tb = sys.exc_info()
                line_number = exc_tb.tb_lineno
                self.logger.error(f"Cloudflare 处理异常 at line {line_number}: {e}")
                
            retries += 1
            if retries < max_retries:
                retry_wait = random.uniform(3, 6)
                self.logger.info(f"等待 {retry_wait:.1f} 秒后重试...")
                time.sleep(retry_wait)
        
        self.logger.warning("❌ Cloudflare 挑战处理失败")
        return False
    
    def test_with_user_agent(self, user_agent: str, url: str = "https://www.nodeseek.com/"):
        """测试特定的 user agent"""
        self.logger.info(f"🧪 测试 User-Agent: {user_agent[:50]}...")
        
        try:
            # 创建页面
            page = self.create_stealth_page(user_agent)
            
            # 导航
            self.logger.info(f"导航到 {url}")
            page.get(url)
            
            # 等待初始加载
            time.sleep(5)
            
            # 处理 Cloudflare
            success = self.enhanced_cloudflare_handler(page)
            
            if success:
                self.logger.info("🎉 SUCCESS! 成功绕过 Cloudflare")
                
                # 验证页面内容
                final_title = page.title
                self.logger.info(f"最终页面标题: {final_title}")
                
                # 检查页面内容
                try:
                    page_text = page.html[:500].lower()
                    if 'nodeseek' in page_text or 'forum' in page_text:
                        self.logger.info("✅ 页面内容验证成功！")
                    else:
                        self.logger.warning("⚠️ 页面内容可能不完整")
                except:
                    pass
                
                # 保持浏览器打开供观察
                self.logger.info("保持浏览器打开 30 秒供观察...")
                time.sleep(30)
            else:
                self.logger.error("❌ 未能绕过 Cloudflare")
            
            page.close()
            return success
            
        except Exception as e:
            self.logger.error(f"测试失败: {e}")
            return False


def main():
    """主函数"""
    bypass = AggressiveCloudflareBypass()
    
    print("🛡️ 激进 Cloudflare 绕过测试")
    print("=" * 50)
    
    # 测试所有用户代理
    for i, ua in enumerate(bypass.proven_user_agents, 1):
        print(f"\n🔄 测试 {i}/{len(bypass.proven_user_agents)}")
        print(f"User-Agent: {ua}")
        print("-" * 50)
        
        success = bypass.test_with_user_agent(ua)
        
        if success:
            print(f"🎉 找到有效的 User-Agent!")
            print(f"推荐使用: {ua}")
            break
        else:
            print(f"❌ 此 User-Agent 无效")
            
        # 等待一段时间再测试下一个
        if i < len(bypass.proven_user_agents):
            print("等待 10 秒后测试下一个...")
            time.sleep(10)


if __name__ == "__main__":
    main()
