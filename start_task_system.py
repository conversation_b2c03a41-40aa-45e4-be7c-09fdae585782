#!/usr/bin/env python3
"""
Startup Script for Web Crawling Task Management System
Initializes and starts the complete task management system
"""

import os
import sys
import json
import logging
import argparse
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import uvicorn
from task_management_api import app, task_manager, execution_engine
from improved_forum_crawler import CrawlerConfig, CrawlerLogger


def setup_logging(config_path: str = "task_system_config.json"):
    """Setup logging configuration"""
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        
        log_config = config.get('logging', {})
        
        # Setup structured logging
        logging.basicConfig(
            level=getattr(logging, log_config.get('level', 'INFO')),
            format=log_config.get('format', 
                '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'),
            handlers=[
                logging.FileHandler(log_config.get('file', 'task_system.log'), encoding='utf-8'),
                logging.StreamHandler(sys.stdout) if log_config.get('console_output', True) else None
            ]
        )
        
        # Set specific logger levels
        logging.getLogger('uvicorn').setLevel(logging.INFO)
        logging.getLogger('fastapi').setLevel(logging.INFO)
        logging.getLogger('DrissionPage').setLevel(logging.WARNING)
        
        return logging.getLogger('task_system')
        
    except Exception as e:
        print(f"Failed to setup logging: {e}")
        logging.basicConfig(level=logging.INFO)
        return logging.getLogger('task_system')


def validate_dependencies():
    """Validate that all required dependencies are installed"""
    required_modules = [
        'fastapi', 'uvicorn', 'pydantic', 'DrissionPage', 
        'redis', 'sqlite3', 'threading', 'asyncio'
    ]
    
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"❌ Missing required modules: {', '.join(missing_modules)}")
        print("Please install them using: pip install -r task_system_requirements.txt")
        return False
    
    return True


def check_configuration():
    """Check if configuration files exist and are valid"""
    config_files = [
        'task_system_config.json',
        'crawler_config.json'
    ]
    
    for config_file in config_files:
        if not os.path.exists(config_file):
            print(f"⚠️  Configuration file {config_file} not found")
            
            if config_file == 'crawler_config.json':
                print("Creating default crawler configuration...")
                create_default_crawler_config()
            else:
                print(f"Please create {config_file} or copy from template")
                return False
    
    return True


def create_default_crawler_config():
    """Create default crawler configuration if it doesn't exist"""
    default_config = {
        "base_url": "https://lowendtalk.com/",
        "monitor_url": "https://lowendtalk.com/categories/offers",
        "state_file": "task_system_state.json",
        "results_file": "task_system_results.json",
        "flash_sale_keywords": [
            "offer", "sale", "discount", "promo", "limited", "flash sale",
            "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth",
            "$/month", "$/yr", "price", "deal", "special", "promotion",
            "cheap", "budget", "affordable", "exclusive", "limited time"
        ],
        "num_workers": 5,
        "monitor_timeout": 90,
        "worker_timeout": 30,
        "queue_timeout": 30,
        "headless": True,
        "refresh_interval": 60,
        "page_load_delay": 3.0,
        "min_request_delay": 1.0,
        "max_request_delay": 3.0,
        "max_retries": 3,
        "redis_enabled": True,
        "redis_host": "localhost",
        "redis_port": 6379,
        "redis_db": 0
    }
    
    with open('crawler_config.json', 'w') as f:
        json.dump(default_config, f, indent=2)
    
    print("✅ Created default crawler_config.json")


def check_redis_connection():
    """Check if Redis is available and accessible"""
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=5)
        r.ping()
        print("✅ Redis connection successful")
        return True
    except Exception as e:
        print(f"⚠️  Redis connection failed: {e}")
        print("Redis is optional but recommended for distributed locking")
        return False


def initialize_database():
    """Initialize the task database"""
    try:
        # This will create the database and tables if they don't exist
        task_manager._init_db()
        print("✅ Database initialized successfully")
        return True
    except Exception as e:
        print(f"❌ Database initialization failed: {e}")
        return False


def print_startup_banner():
    """Print startup banner with system information"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Web Crawling Task Management System                       ║
║                           Version 1.0.0                                     ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🚀 REST API for managing forum post crawling tasks                         ║
║  🌐 Multi-domain browser management with tab isolation                      ║
║  🔄 Real-time incremental comment processing                                ║
║  🤖 AI-powered flash sale and pre-sale chat detection                       ║
║  📊 Redis-based distributed locking and state management                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def print_api_endpoints():
    """Print available API endpoints"""
    endpoints = """
📡 Available API Endpoints:
┌─────────────────────────────────────────────────────────────────────────────┐
│ POST   /tasks                    - Create new crawling task                 │
│ GET    /tasks                    - List all tasks                           │
│ GET    /tasks/{id}               - Get specific task details                │
│ PUT    /tasks/{id}/start         - Start monitoring a task                  │
│ PUT    /tasks/{id}/stop          - Stop monitoring a task                   │
│ DELETE /tasks/{id}               - Delete a task                            │
│ POST   /tasks/stop-all           - Stop all running tasks                   │
│ GET    /                         - API health check                         │
└─────────────────────────────────────────────────────────────────────────────┘
    """
    print(endpoints)


def main():
    """Main startup function"""
    parser = argparse.ArgumentParser(description='Web Crawling Task Management System')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload for development')
    parser.add_argument('--workers', type=int, default=1, help='Number of worker processes')
    parser.add_argument('--config', default='task_system_config.json', help='Configuration file path')
    
    args = parser.parse_args()
    
    # Print startup banner
    print_startup_banner()
    
    # Setup logging
    logger = setup_logging(args.config)
    logger.info("Starting Web Crawling Task Management System")
    
    # Validate dependencies
    print("🔍 Validating dependencies...")
    if not validate_dependencies():
        sys.exit(1)
    
    # Check configuration
    print("⚙️  Checking configuration...")
    if not check_configuration():
        sys.exit(1)
    
    # Check Redis connection
    print("🔗 Checking Redis connection...")
    check_redis_connection()
    
    # Initialize database
    print("💾 Initializing database...")
    if not initialize_database():
        sys.exit(1)
    
    # Print API information
    print_api_endpoints()
    
    # Start the server
    print(f"🚀 Starting server on {args.host}:{args.port}")
    print(f"📖 API documentation available at: http://{args.host}:{args.port}/docs")
    print(f"🔧 Alternative docs at: http://{args.host}:{args.port}/redoc")
    print("\n" + "="*80)
    
    try:
        uvicorn.run(
            "task_management_api:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level="info",
            access_log=True
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down gracefully...")
        logger.info("Server shutdown requested")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        logger.error(f"Server startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
