{"cloudflare_bypass": {"enabled": true, "use_stealth_mode": true, "headless": false, "challenge_timeout": 30, "navigation_retries": 3, "human_behavior_simulation": true}, "browser_settings": {"disable_images": false, "disable_javascript": false, "disable_css": false, "disable_plugins": true, "disable_extensions": true, "window_size": "random", "user_agent": "random"}, "timing_settings": {"page_load_delay": [3, 6], "navigation_delay": [2, 5], "challenge_wait": [1, 3], "human_action_delay": [0.5, 1.5], "scroll_delay": [0.5, 1.5]}, "detection_evasion": {"hide_webdriver": true, "fake_plugins": true, "randomize_canvas": true, "spoof_timezone": false, "custom_headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8", "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8", "Accept-Encoding": "gzip, deflate, br", "Cache-Control": "max-age=0", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Dest": "document", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-Site": "none", "Sec-Fetch-User": "?1"}}, "user_agents": ["Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0", "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 Edg/120.0.0.0"], "viewports": [[1920, 1080], [1366, 768], [1536, 864], [1440, 900], [1280, 720], [1600, 900], [2560, 1440], [1920, 1200]], "cloudflare_indicators": ["checking your browser before accessing", "ddos protection by cloudflare", "cf-browser-verification", "cf-challenge-form", "cloudflare", "just a moment", "please wait while we check your browser", "ray id", "performance & security by cloudflare"], "success_indicators": ["<!doctype html", "<html", "<head>", "<body>", "content-type"], "retry_strategies": {"exponential_backoff": true, "base_delay": 5, "max_delay": 60, "jitter": true}}