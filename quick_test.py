#!/usr/bin/env python3
"""Quick test of the Task Management API"""

import requests
import json
import time

API_BASE = "http://127.0.0.1:8001"

def test_api():
    print("🧪 Testing Task Management API...")
    
    # Test health check
    print("\n1. Testing health check...")
    response = requests.get(f"{API_BASE}/")
    print(f"   Status: {response.status_code}")
    print(f"   Response: {response.json()}")
    
    # Test creating a task
    print("\n2. Testing task creation...")
    task_data = {
        "post_url": "https://lowendtalk.com/discussion/test-post",
        "forum_domain": "lowendtalk.com",
        "monitor_interval": 60,
        "ai_analysis_enabled": True
    }
    
    response = requests.post(f"{API_BASE}/tasks", json=task_data)
    print(f"   Status: {response.status_code}")
    if response.status_code == 200:
        task = response.json()
        task_id = task["id"]
        print(f"   Created task: {task_id}")
        print(f"   Task status: {task['status']}")
        
        # Test listing tasks
        print("\n3. Testing task listing...")
        response = requests.get(f"{API_BASE}/tasks")
        print(f"   Status: {response.status_code}")
        tasks = response.json()
        print(f"   Found {len(tasks)} tasks")
        
        # Test getting specific task
        print("\n4. Testing get specific task...")
        response = requests.get(f"{API_BASE}/tasks/{task_id}")
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            task_details = response.json()
            print(f"   Task ID: {task_details['id'][:8]}...")
            print(f"   Status: {task_details['status']}")
            print(f"   URL: {task_details['post_url']}")
        
        # Clean up - delete the test task
        print("\n5. Cleaning up test task...")
        response = requests.delete(f"{API_BASE}/tasks/{task_id}")
        print(f"   Delete status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Task deleted successfully")
    else:
        print(f"   Error: {response.text}")
    
    print("\n✅ API test completed!")

if __name__ == "__main__":
    test_api()
