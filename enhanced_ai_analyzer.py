"""
Enhanced AI Analyzer for Web Crawling System
Advanced content analysis for flash sale detection and pre-sale chat patterns
"""

import re
import time
import logging
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass
from enum import Enum

from improved_forum_crawler import FlashSaleDetector, CrawlerConfig


class ContentType(str, Enum):
    FLASH_SALE = "flash_sale"
    PRE_SALE_CHAT = "pre_sale_chat"
    PRICE_DISCUSSION = "price_discussion"
    AVAILABILITY_UPDATE = "availability_update"
    TECHNICAL_SPECS = "technical_specs"
    GENERAL_DISCUSSION = "general_discussion"


@dataclass
class AnalysisResult:
    """Result of AI content analysis"""
    content_type: ContentType
    confidence: float  # 0.0 to 1.0
    keywords_matched: List[str]
    extracted_data: Dict[str, any]
    timestamp: float
    comment_text: str


class EnhancedAIAnalyzer:
    """
    Enhanced AI analyzer that extends the basic flash sale detection
    with advanced pattern recognition and content classification
    """
    
    def __init__(self, config: CrawlerConfig):
        self.config = config
        self.logger = logging.getLogger("enhanced_ai_analyzer")
        
        # Initialize base flash sale detector
        self.flash_sale_detector = FlashSaleDetector(config)
        
        # Enhanced keyword patterns
        self.patterns = self._initialize_patterns()
        
        # Price extraction patterns
        self.price_patterns = [
            r'\$(\d+(?:\.\d{2})?)\s*(?:/\s*(?:month|mo|year|yr))?',
            r'(\d+(?:\.\d{2})?)\s*(?:USD|dollars?)\s*(?:/\s*(?:month|mo|year|yr))?',
            r'€(\d+(?:\.\d{2})?)\s*(?:/\s*(?:month|mo|year|yr))?',
            r'£(\d+(?:\.\d{2})?)\s*(?:/\s*(?:month|mo|year|yr))?'
        ]
        
        # Technical specs patterns
        self.spec_patterns = {
            'ram': r'(\d+)\s*(?:GB|MB)\s*(?:RAM|memory)',
            'storage': r'(\d+)\s*(?:GB|TB|MB)\s*(?:SSD|HDD|NVMe|storage|disk)',
            'cpu': r'(\d+)\s*(?:core|CPU|vCPU|processor)',
            'bandwidth': r'(\d+)\s*(?:GB|TB|MB)\s*(?:bandwidth|transfer|traffic)',
            'ip': r'(\d+)\s*(?:IP|IPv4|IPv6)',
            'location': r'(?:located|datacenter|DC)\s+(?:in\s+)?([A-Za-z\s,]+)'
        }
        
        # Pre-sale chat indicators
        self.pre_sale_indicators = [
            r'when\s+(?:will|is)\s+(?:this|it)\s+(?:be\s+)?(?:available|released|launched)',
            r'(?:any\s+)?(?:eta|estimate|timeline)\s+(?:on\s+)?(?:when|for)',
            r'(?:coming\s+)?soon\s*\?',
            r'(?:will\s+)?(?:this\s+)?(?:be\s+)?(?:available|released)\s+(?:soon|today|tomorrow)',
            r'(?:waiting\s+)?(?:for\s+)?(?:the\s+)?(?:announcement|release|launch)',
            r'(?:any\s+)?(?:news|updates?)\s+(?:on\s+)?(?:when|availability)'
        ]
        
        # Availability update patterns
        self.availability_patterns = [
            r'(?:now\s+)?(?:available|in\s+stock|live)',
            r'(?:just\s+)?(?:launched|released|went\s+live)',
            r'(?:sold\s+)?out',
            r'(?:back\s+)?in\s+stock',
            r'(?:limited\s+)?(?:quantity|stock)\s+(?:available|remaining)',
            r'(?:only\s+)?(\d+)\s+(?:left|remaining|available)'
        ]
        
        self.logger.info("EnhancedAIAnalyzer initialized")
    
    def analyze_comment(self, comment_text: str, post_context: Optional[Dict] = None) -> List[AnalysisResult]:
        """
        Perform comprehensive analysis of a comment
        Returns list of analysis results for different content types detected
        """
        results = []
        comment_lower = comment_text.lower()
        
        # 1. Flash Sale Detection (using existing detector)
        if self.flash_sale_detector.analyze_comment(comment_text):
            flash_sale_result = self._analyze_flash_sale(comment_text)
            if flash_sale_result:
                results.append(flash_sale_result)
        
        # 2. Pre-sale Chat Detection
        pre_sale_result = self._analyze_pre_sale_chat(comment_text)
        if pre_sale_result:
            results.append(pre_sale_result)
        
        # 3. Price Discussion Analysis
        price_result = self._analyze_price_discussion(comment_text)
        if price_result:
            results.append(price_result)
        
        # 4. Availability Update Detection
        availability_result = self._analyze_availability_update(comment_text)
        if availability_result:
            results.append(availability_result)
        
        # 5. Technical Specs Extraction
        specs_result = self._analyze_technical_specs(comment_text)
        if specs_result:
            results.append(specs_result)
        
        return results
    
    def _analyze_flash_sale(self, comment_text: str) -> Optional[AnalysisResult]:
        """Analyze comment for flash sale content"""
        keywords_matched = []
        extracted_data = {}
        
        # Extract prices
        prices = self._extract_prices(comment_text)
        if prices:
            extracted_data['prices'] = prices
        
        # Extract technical specs
        specs = self._extract_technical_specs(comment_text)
        if specs:
            extracted_data['specs'] = specs
        
        # Check for urgency indicators
        urgency_score = self._calculate_urgency_score(comment_text)
        extracted_data['urgency_score'] = urgency_score
        
        # Match flash sale keywords
        for keyword in self.config.flash_sale_keywords:
            if keyword.lower() in comment_text.lower():
                keywords_matched.append(keyword)
        
        # Calculate confidence based on multiple factors
        confidence = self._calculate_flash_sale_confidence(
            comment_text, keywords_matched, extracted_data
        )
        
        if confidence > 0.3:  # Threshold for flash sale detection
            return AnalysisResult(
                content_type=ContentType.FLASH_SALE,
                confidence=confidence,
                keywords_matched=keywords_matched,
                extracted_data=extracted_data,
                timestamp=time.time(),
                comment_text=comment_text
            )
        
        return None
    
    def _analyze_pre_sale_chat(self, comment_text: str) -> Optional[AnalysisResult]:
        """Analyze comment for pre-sale chat patterns"""
        keywords_matched = []
        extracted_data = {}
        
        # Check for pre-sale indicators
        for pattern in self.pre_sale_indicators:
            if re.search(pattern, comment_text, re.IGNORECASE):
                keywords_matched.append(pattern)
        
        # Look for question patterns
        question_count = comment_text.count('?')
        if question_count > 0:
            extracted_data['question_count'] = question_count
        
        # Check for anticipation keywords
        anticipation_keywords = ['waiting', 'excited', 'anticipating', 'looking forward', 'can\'t wait']
        for keyword in anticipation_keywords:
            if keyword in comment_text.lower():
                keywords_matched.append(keyword)
        
        confidence = len(keywords_matched) * 0.2 + (question_count * 0.1)
        confidence = min(confidence, 1.0)
        
        if confidence > 0.2:
            return AnalysisResult(
                content_type=ContentType.PRE_SALE_CHAT,
                confidence=confidence,
                keywords_matched=keywords_matched,
                extracted_data=extracted_data,
                timestamp=time.time(),
                comment_text=comment_text
            )
        
        return None
    
    def _analyze_price_discussion(self, comment_text: str) -> Optional[AnalysisResult]:
        """Analyze comment for price-related discussions"""
        keywords_matched = []
        extracted_data = {}
        
        # Extract prices
        prices = self._extract_prices(comment_text)
        if prices:
            extracted_data['prices'] = prices
            keywords_matched.extend([f"${price}" for price in prices])
        
        # Check for price comparison keywords
        price_keywords = ['cheap', 'expensive', 'worth', 'value', 'cost', 'price', 'deal', 'bargain']
        for keyword in price_keywords:
            if keyword in comment_text.lower():
                keywords_matched.append(keyword)
        
        confidence = (len(prices) * 0.3) + (len(keywords_matched) * 0.1)
        confidence = min(confidence, 1.0)
        
        if confidence > 0.2:
            return AnalysisResult(
                content_type=ContentType.PRICE_DISCUSSION,
                confidence=confidence,
                keywords_matched=keywords_matched,
                extracted_data=extracted_data,
                timestamp=time.time(),
                comment_text=comment_text
            )
        
        return None
    
    def _analyze_availability_update(self, comment_text: str) -> Optional[AnalysisResult]:
        """Analyze comment for availability updates"""
        keywords_matched = []
        extracted_data = {}
        
        # Check availability patterns
        for pattern in self.availability_patterns:
            matches = re.findall(pattern, comment_text, re.IGNORECASE)
            if matches:
                keywords_matched.append(pattern)
                if any(char.isdigit() for char in str(matches)):
                    extracted_data['quantity_mentioned'] = matches
        
        # Check for time-sensitive language
        time_keywords = ['now', 'just', 'immediately', 'urgent', 'hurry', 'quick']
        for keyword in time_keywords:
            if keyword in comment_text.lower():
                keywords_matched.append(keyword)
        
        confidence = len(keywords_matched) * 0.25
        confidence = min(confidence, 1.0)
        
        if confidence > 0.25:
            return AnalysisResult(
                content_type=ContentType.AVAILABILITY_UPDATE,
                confidence=confidence,
                keywords_matched=keywords_matched,
                extracted_data=extracted_data,
                timestamp=time.time(),
                comment_text=comment_text
            )
        
        return None
    
    def _analyze_technical_specs(self, comment_text: str) -> Optional[AnalysisResult]:
        """Analyze comment for technical specifications"""
        keywords_matched = []
        extracted_data = {}
        
        # Extract technical specifications
        specs = self._extract_technical_specs(comment_text)
        if specs:
            extracted_data['specs'] = specs
            keywords_matched.extend(specs.keys())
        
        confidence = len(specs) * 0.2
        confidence = min(confidence, 1.0)
        
        if confidence > 0.2:
            return AnalysisResult(
                content_type=ContentType.TECHNICAL_SPECS,
                confidence=confidence,
                keywords_matched=keywords_matched,
                extracted_data=extracted_data,
                timestamp=time.time(),
                comment_text=comment_text
            )
        
        return None
    
    def _extract_prices(self, text: str) -> List[float]:
        """Extract price values from text"""
        prices = []
        for pattern in self.price_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                try:
                    price = float(match)
                    if 0.01 <= price <= 10000:  # Reasonable price range
                        prices.append(price)
                except ValueError:
                    continue
        return list(set(prices))  # Remove duplicates
    
    def _extract_technical_specs(self, text: str) -> Dict[str, str]:
        """Extract technical specifications from text"""
        specs = {}
        for spec_type, pattern in self.spec_patterns.items():
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                specs[spec_type] = matches[0] if isinstance(matches[0], str) else str(matches[0])
        return specs
    
    def _calculate_urgency_score(self, text: str) -> float:
        """Calculate urgency score based on language patterns"""
        urgency_keywords = [
            'limited time', 'hurry', 'quick', 'fast', 'urgent', 'now', 'immediately',
            'while supplies last', 'limited quantity', 'few left', 'almost gone',
            'ending soon', 'expires', 'deadline', 'last chance'
        ]
        
        score = 0.0
        text_lower = text.lower()
        
        for keyword in urgency_keywords:
            if keyword in text_lower:
                score += 0.1
        
        # Check for exclamation marks
        score += text.count('!') * 0.05
        
        # Check for capital letters (shouting)
        caps_ratio = sum(1 for c in text if c.isupper()) / max(len(text), 1)
        if caps_ratio > 0.3:
            score += 0.2
        
        return min(score, 1.0)
    
    def _calculate_flash_sale_confidence(self, text: str, keywords: List[str], 
                                       extracted_data: Dict) -> float:
        """Calculate confidence score for flash sale detection"""
        confidence = 0.0
        
        # Base confidence from keywords
        confidence += len(keywords) * 0.1
        
        # Boost for price information
        if 'prices' in extracted_data:
            confidence += len(extracted_data['prices']) * 0.2
        
        # Boost for technical specs
        if 'specs' in extracted_data:
            confidence += len(extracted_data['specs']) * 0.1
        
        # Boost for urgency
        if 'urgency_score' in extracted_data:
            confidence += extracted_data['urgency_score'] * 0.3
        
        return min(confidence, 1.0)
    
    def _initialize_patterns(self) -> Dict[str, List[str]]:
        """Initialize enhanced keyword patterns"""
        return {
            'flash_sale': self.config.flash_sale_keywords,
            'urgency': [
                'limited time', 'hurry', 'quick', 'fast', 'urgent', 'now',
                'while supplies last', 'limited quantity', 'ending soon'
            ],
            'pricing': [
                'price', 'cost', 'deal', 'discount', 'sale', 'offer',
                'cheap', 'affordable', 'budget', 'value'
            ],
            'technical': [
                'ram', 'cpu', 'storage', 'bandwidth', 'ssd', 'nvme',
                'core', 'gb', 'tb', 'mb', 'ip', 'datacenter'
            ]
        }
