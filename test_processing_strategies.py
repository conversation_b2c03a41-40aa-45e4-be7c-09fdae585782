#!/usr/bin/env python3
"""
Test script to verify both last-page-first and traditional processing strategies
"""

import sys
import os

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import ForumCrawler, CrawlerConfig


def test_configuration_options():
    """Test that configuration options are loaded correctly"""
    print("Testing configuration options...")
    
    # Test loading from file
    config = CrawlerConfig.from_file("crawler_config.json")
    
    print(f"Use last-page-first: {config.use_last_page_first}")
    
    # Test default value
    default_config = CrawlerConfig()
    print(f"Default last-page-first: {default_config.use_last_page_first}")
    
    assert hasattr(config, 'use_last_page_first'), "Configuration missing use_last_page_first option"
    print("✅ Configuration options test PASSED")


def test_processing_strategy_selection():
    """Test that the correct processing strategy is selected"""
    print("\nTesting processing strategy selection...")
    
    # Test with last-page-first enabled
    config_optimized = CrawlerConfig()
    config_optimized.use_last_page_first = True
    crawler_optimized = ForumCrawler(config_optimized)
    
    # Test with last-page-first disabled
    config_traditional = CrawlerConfig()
    config_traditional.use_last_page_first = False
    crawler_traditional = ForumCrawler(config_traditional)
    
    print(f"Optimized crawler uses last-page-first: {crawler_optimized.config.use_last_page_first}")
    print(f"Traditional crawler uses last-page-first: {crawler_traditional.config.use_last_page_first}")
    
    assert crawler_optimized.config.use_last_page_first == True
    assert crawler_traditional.config.use_last_page_first == False
    
    print("✅ Processing strategy selection test PASSED")


def test_url_building_consistency():
    """Test that URL building is consistent between strategies"""
    print("\nTesting URL building consistency...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    base_url = "https://lowendtalk.com/discussion/test"
    test_cases = [
        {"comments": 25, "expected_page": 1, "expected_suffix": ""},
        {"comments": 40, "expected_page": 2, "expected_suffix": "/p2"},
        {"comments": 90, "expected_page": 3, "expected_suffix": "/p3"},
        {"comments": 167, "expected_page": 6, "expected_suffix": "/p6"},
    ]
    
    for case in test_cases:
        comment_count = case["comments"]
        expected_page = case["expected_page"]
        expected_url = base_url + case["expected_suffix"]
        
        # Test last page calculation
        last_page = crawler._calculate_last_page(comment_count)
        assert last_page == expected_page, f"Last page calculation failed for {comment_count} comments"
        
        # Test URL building
        last_page_url, page_num = crawler._build_last_page_url(base_url, comment_count)
        assert last_page_url == expected_url, f"URL building failed for {comment_count} comments"
        assert page_num == expected_page, f"Page number mismatch for {comment_count} comments"
        
        print(f"✓ {comment_count:3d} comments -> Page {page_num}, URL: {last_page_url}")
    
    print("✅ URL building consistency test PASSED")


def test_performance_benefits():
    """Test and demonstrate performance benefits"""
    print("\nTesting performance benefits...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    test_scenarios = [
        {"name": "Small post", "comments": 50},
        {"name": "Medium post", "comments": 150},
        {"name": "Large post", "comments": 300},
        {"name": "Very large post", "comments": 600},
    ]
    
    print("\nPerformance comparison:")
    print(f"{'Scenario':<15} {'Comments':<10} {'Traditional':<12} {'Optimized':<10} {'Improvement':<12}")
    print("-" * 65)
    
    total_traditional = 0
    total_optimized = 0
    
    for scenario in test_scenarios:
        name = scenario["name"]
        comment_count = scenario["comments"]
        
        # Traditional: need to process all pages from start
        traditional_pages = crawler._calculate_last_page(comment_count)
        
        # Optimized: only need to process last page
        optimized_pages = 1
        
        improvement = traditional_pages - optimized_pages
        improvement_pct = (improvement / traditional_pages) * 100 if traditional_pages > 0 else 0
        
        total_traditional += traditional_pages
        total_optimized += optimized_pages
        
        print(f"{name:<15} {comment_count:<10} {traditional_pages:<12} {optimized_pages:<10} {improvement} ({improvement_pct:.0f}%)")
    
    total_improvement = total_traditional - total_optimized
    total_improvement_pct = (total_improvement / total_traditional) * 100 if total_traditional > 0 else 0
    
    print("-" * 65)
    print(f"{'TOTAL':<15} {'':<10} {total_traditional:<12} {total_optimized:<10} {total_improvement} ({total_improvement_pct:.0f}%)")
    
    print(f"\nOverall performance improvement: {total_improvement_pct:.0f}% reduction in page loads")
    print("✅ Performance benefits test PASSED")


def test_edge_cases():
    """Test edge cases for the optimization"""
    print("\nTesting edge cases...")
    
    config = CrawlerConfig()
    crawler = ForumCrawler(config)
    
    edge_cases = [
        {"comments": 0, "expected_page": 1, "description": "Zero comments"},
        {"comments": 1, "expected_page": 1, "description": "Single comment"},
        {"comments": 30, "expected_page": 1, "description": "Exactly 30 comments (1 page)"},
        {"comments": 31, "expected_page": 2, "description": "31 comments (boundary case)"},
        {"comments": 1000, "expected_page": 34, "description": "Very large post"},
    ]
    
    for case in edge_cases:
        comment_count = case["comments"]
        expected_page = case["expected_page"]
        description = case["description"]
        
        actual_page = crawler._calculate_last_page(comment_count)
        
        if actual_page == expected_page:
            print(f"✓ {description}: {comment_count} comments -> Page {actual_page}")
        else:
            print(f"✗ {description}: {comment_count} comments -> Page {actual_page} (expected {expected_page})")
            assert False, f"Edge case failed: {description}"
    
    print("✅ Edge cases test PASSED")


def main():
    """Run all tests"""
    print("Processing Strategies Test Suite")
    print("=" * 50)
    
    try:
        test_configuration_options()
        test_processing_strategy_selection()
        test_url_building_consistency()
        test_performance_benefits()
        test_edge_cases()
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("\nLast-page-first optimization is ready for use!")
        print("\nKey benefits:")
        print("• 50-95% reduction in page loads")
        print("• Immediate access to newest comments")
        print("• Faster flash sale detection")
        print("• Configurable fallback to traditional processing")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
