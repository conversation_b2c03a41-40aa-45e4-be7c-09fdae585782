{"extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": {"account_extension_type": 0, "active_permissions": {"api": ["management", "system.display", "system.storage", "webstorePrivate", "system.cpu", "system.memory", "system.network"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "app_launcher_ordinal": "t", "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"app": {"launch": {"web_url": "https://chrome.google.com/webstore"}, "urls": ["https://chrome.google.com/webstore"]}, "description": "查找适用于Google Chrome的精彩应用、游戏、扩展程序和主题背景。", "icons": {"128": "webstore_icon_128.png", "16": "webstore_icon_16.png"}, "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCtl3tO0osjuzRsf6xtD2SKxPlTfuoy7AWoObysitBPvH5fE1NaAA1/2JkPWkVDhdLBWLaIBPYeXbzlHp3y4Vv/4XG+aN5qFE3z+1RU/NqkzVYHtIpVScf3DjTYtKVL66mzVGijSoAIwbFCC3LpGdaoe6Q1rSRDp76wR6jjFzsYwQIDAQAB", "name": "应用商店", "permissions": ["webstorePrivate", "management", "system.cpu", "system.display", "system.memory", "system.network", "system.storage"], "version": "0.2"}, "needs_sync": true, "page_ordinal": "n", "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\web_store", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "fignfifoniblkonapihmkfakmlgkbkcf": {"account_extension_type": 0, "active_permissions": {"api": ["metricsPrivate", "systemPrivate", "ttsEngine", "offscreen"], "explicit_host": ["https://www.google.com/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"service_worker": "service_worker.js"}, "description": "Component extension providing speech via the Google network text-to-speech service.", "host_permissions": ["https://www.google.com/"], "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA5mnqF6oM8Q5tYd7YqL40YL7Keftt4PwydehlNOyNlCiWDM/7SiQYwxYvVHMj1i03z7B5lZXQinrcqhHhoIgcSHK1JrdzVSJxPRVdmV0rJLv0KQgmVwL8p8MfN6SmHs+72xz+1GoRWpd0WlHMil7RzGKJA4Ku+9jxxsXoxes9eeV1hCavkb1dSF+mlQbaNiw7u1hhvc5mmeuEcWjoce8r8B2R4wmnGbuTLfoSchZ6jkasynmOaFxyT4jiYDYgrNtWRTQ/9PuPduJ+uBWVT/o2ZhDK2XcywVwzUfYIXDLDblK+YdZi8w8ZBNvc7hP9/iZr6/eoUpfsLa8qlJgyLBQebwIDAQAB", "manifest_version": 3, "name": "Google Network Speech", "permissions": ["metricsPrivate", "offscreen", "systemPrivate", "ttsEngine"], "tts_engine": {"voices": [{"event_types": ["start", "end", "error"], "gender": "female", "lang": "de-DE", "remote": true, "voice_name": "Google Deutsch"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-US", "remote": true, "voice_name": "Google US English"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Female"}, {"event_types": ["start", "end", "error"], "gender": "male", "lang": "en-GB", "remote": true, "voice_name": "Google UK English Male"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-ES", "remote": true, "voice_name": "Google español"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "es-US", "remote": true, "voice_name": "Google español de Estados Unidos"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "fr-FR", "remote": true, "voice_name": "Google français"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "hi-IN", "remote": true, "voice_name": "Google हिन्दी"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "id-ID", "remote": true, "voice_name": "Google Bahasa Indonesia"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "it-IT", "remote": true, "voice_name": "Google italiano"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ja-<PERSON>", "remote": true, "voice_name": "Google 日本語"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ko-KR", "remote": true, "voice_name": "Google 한국의"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "nl-NL", "remote": true, "voice_name": "Google Nederlands"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pl-PL", "remote": true, "voice_name": "Google polski"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "pt-BR", "remote": true, "voice_name": "Google português do Brasil"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "ru-RU", "remote": true, "voice_name": "Google русский"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-CN", "remote": true, "voice_name": "Google 普通话（中国大陆）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-HK", "remote": true, "voice_name": "Google 粤語（香港）"}, {"event_types": ["start", "end", "error"], "gender": "female", "lang": "zh-TW", "remote": true, "voice_name": "Google 國語（臺灣）"}]}, "version": "1.0"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\network_speech_synthesis/mv3", "preferences": {}, "regular_only_preferences": {}, "service_worker_registration_info": {"version": "1.0"}, "serviceworkerevents": ["ttsEngine.onPause", "ttsEngine.onResume", "ttsEngine.onSpeak", "ttsEngine.onStop"], "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate"], "explicit_host": ["chrome://resources/*", "chrome://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "index.html", "name": "Chrome PDF Viewer", "offline_enabled": true, "permissions": ["chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write"]}], "version": "1"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "nhkjnlcggomjhckdeamipedlomphkepc": {"account_extension_type": 0, "ack_prompt_count": 1, "active_bit": false, "active_permissions": {"api": ["clipboardRead", "clipboardWrite", "cookies", "debugger", "downloads", "management", "nativeMessaging", "tabs", "webNavigation"], "explicit_host": ["<all_urls>", "chrome://favicon/*"], "manifest_permissions": [], "scriptable_host": ["file:///*", "ftp://*/*", "http://*/*", "https://*/*"]}, "allowlist": 1, "commands": {}, "content_settings": [], "creation_flags": 4097, "cws-info": {"is-live": true, "is-present": true, "last-updated-time-millis": "*************", "no-privacy-practice": true, "unpublished-long-ago": false, "violation-type": 0}, "disable_reasons": [8192], "external_first_run": true, "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 3, "manifest": {"background": {"persistent": true, "scripts": ["background.static.js"]}, "content_scripts": [{"all_frames": true, "js": ["content.static.js"], "match_about_blank": true, "matches": ["http://*/*", "https://*/*", "ftp://*/*", "file://*/*"], "run_at": "document_start"}], "content_security_policy": "script-src 'self' 'unsafe-eval'; object-src 'self'", "description": "影刀Chrome自动化插件", "icons": {"128": "icons/shadow128.png", "16": "icons/shadow16.png", "32": "icons/shadow32.png", "48": "icons/shadow64.png"}, "key": "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAmUT1sxTgUJMxTNVUfUbExYYSQ9zBaPr/T060kWmikxzXByFzbKzb4hQOwkmkv0lUmIsET/niZuRtO1CHd2tr9GwhtEXEzbVzuzPdxgay4lDcMpN62VC6tVmk6MBpfzolLky7EpbRfLboDepXet7xyc0yMm+Urf4eO+srETjXvA4B5isbsaX4Z/XS2EdDP6ZYMJNfoagiy/tYnZ4aHigL8/lTsSGgp9/gnit/e9nUfHVK768xyYHwmPE2H5ECoIvp7/Dtsgb6li1mbnxB28gjZ0wc6lg/wNIKpWW3RBikBAKxxrzht8hfTUHJZDaJdDzlc8yBiiLJ2AI2tD9dMz0TAQIDAQAB", "manifest_version": 2, "name": "影刀", "permissions": ["cookies", "management", "tabs", "debugger", "nativeMessaging", "downloads", "webNavigation", "clipboardRead", "clipboardWrite", "<all_urls>"], "update_url": "https://clients2.google.com/service/update2/crx", "version": "1.1"}, "path": "nhkjnlcggomjhckdeamipedlomphkepc\\1.1_0", "pending_on_installed_event_dispatch_info": {"previous_version": ""}, "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false, "withholding_permissions": false}, "nkeimhogjdpnpccoofpliimaahmaaome": {"account_extension_type": 0, "active_permissions": {"api": ["processes", "webrtcLoggingPrivate", "system.cpu", "enterprise.hardwarePlatform"], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": ["runtime.onConnectExternal"], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"background": {"page": "background.html", "persistent": false}, "externally_connectable": {"ids": ["moklfjoegmpoolceggbebbmgbddlhdgp", "ldmpofkllgeicjiihkimgeccbhghhmfj", "denipklgekfpcdmbahmbpnmokgajnhma", "kjfhgcncjdebkoofmbjoiemiboifnpbo", "ikfcpmgefdpheiiomgmhlmmkihchmdlj", "jlgegmdnodfhciolbdjciihnlaljdbjo", "lkbhffjfgpmpeppncnimiiikojibkhnm", "acdafoiapclbpdkhnighhilgampkglpc", "hkamnlhnogggfddmjomgbdokdkgfelgg"], "matches": ["https://*.meet.google.com/*"]}, "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDAQt2ZDdPfoSe/JI6ID5bgLHRCnCu9T36aYczmhw/tnv6QZB2I6WnOCMZXJZlRdqWc7w9jo4BWhYS50Vb4weMfh/I0On7VcRwJUgfAxW2cHB+EkmtI1v4v/OU24OqIa1Nmv9uRVeX0GjhQukdLNhAE6ACWooaf5kqKlCeK+1GOkQIDAQAB", "manifest_version": 2, "name": "Google Hangouts", "permissions": ["enterprise.hardwarePlatform", "processes", "system.cpu", "webrtcLoggingPrivate"], "version": "1.3.24"}, "path": "C:\\Program Files\\Google\\Chrome\\Application\\138.0.7204.97\\resources\\hangout_services", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "pinned_tabs": [], "protection": {"macs": {"account_values": {"browser": {"show_home_button": "1C76D0A24BA2A93860BAE7A264E5E4FCAE87F629146AC0BC5B8469C2E02DF402"}, "extensions": {"ui": {"developer_mode": "6D0FCE54CD552E0C0736844102C410E0191A2189370B3E64A6D7D53F74D2AF17"}}, "homepage": "89BA634E905737E8F93AFFF236939965477BD4B39FFE9647151FB1468D95F33B", "homepage_is_newtabpage": "3B0FADF4F432B092370A4FE21FF8E154DB988570008BD55C97DD127571E5BB71", "session": {"restore_on_startup": "C001FF1EC31EA3F4C37B3CCAAD0BC83FC47ED497ED61145FB06753F21AEAFC01", "startup_urls": "AA00337A1CA76B8695345B4F099A9DA8735EB747717E59D9CAB41ABF0C6CA4C9"}}, "browser": {"show_home_button": "B76B597FE5EEF3F49EE3ABF8663A18135EAF0A4CDCC5A0BC19A0F593C64C297B"}, "default_search_provider_data": {"template_url_data": "047B91E227E0E0F16A9A3020750728C777F2802E8FA2C7F56EFDC48C731B6E82"}, "enterprise_signin": {"policy_recovery_token": "A74C31DDB184C427065DA13FB1C1868955B4F44B2DC0D51277E88DCC98F0943B"}, "extensions": {"settings": {"ahfgeienlihckogmohjhadlkjgocpleb": "AF89958FD7F1351D8C53C4011D5D5434440CAEF65E3E21C87BA4B7ADB1B7C707", "fignfifoniblkonapihmkfakmlgkbkcf": "66F50641A69F5934A11879817091BA837B1EA7B3B4BE684A82374398193517CE", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "52C340446D0065D81545737798195FACC58B3FC280465E3BFB86C75170A36143", "nhkjnlcggomjhckdeamipedlomphkepc": "8E3C83AE2EB9B3FED2671BAC983B698EB5DF83CC9A6CE47B73CFD457A1725FF2", "nkeimhogjdpnpccoofpliimaahmaaome": "C492E4991D7E6FCC30A67912B1A677A5C64ADF8AEEF037761F8421E9F5915319"}, "ui": {"developer_mode": "EFAC08F56E9793F2EBD14095BB1569DFD367EEDB5ABB0FF13C9D8D9CB6224FC4"}}, "google": {"services": {"account_id": "D1839DBEBC145ADDEB3D6DE5FED0CAF5B427DCB54A49CD94D134C86D70A51BA8", "last_signed_in_username": "2C2EEDF0C7B73527A2B775F9A22C2B81EE50C0A39AA4A6935FBFEF19096EBC35", "last_username": "5AD354EF146D5C48F237829F42EBF68E352329475A872735940B1C8ACED235B7"}}, "homepage": "A7F040BFE6872CA2AD91156BDD36F53F3E7C486374BD62D5C87E1DE0BD22E7DB", "homepage_is_newtabpage": "2C5160D54E63C5B56387697B56EB969493E005CC6CAE261A247CB35F67A60C15", "media": {"cdm": {"origin_data": "5F3959EDEC518AEBA9F815B4B256A25BE7F074DBBB79BA36A5FF75A8A8DC9AB2"}, "storage_id_salt": "5D8EA93222CA865C06B1BDAA71F2EC1A8262C6934DCF085F0EAD6657FEC381BB"}, "module_blocklist_cache_md5_digest": "0C4D43FB18309244E4DCB485958715B1A96A6E97897FCE03335EE61FF585C342", "pinned_tabs": "D2C5F21CAA8900259711D9E78C686DEF8AC50C0DC110944240B3A7CB6116BA94", "prefs": {"preference_reset_time": "26D8DFAD7B6811DC7E4120A105A210AF4A02C2071ED16A4E0896EC9C9B39518F"}, "safebrowsing": {"incidents_sent": "6C8A2E828A2F2801AFA8B17B20CF0174B43529DB7616D4648BE3F5771B6149C0"}, "search_provider_overrides": "94FCF3517A569E44818276F280BA5906D5929E45EA2DFBBEAFB87625B62AC378", "session": {"restore_on_startup": "EBD2130D64D213D294E4FAC8FBBE316FD323CBCA3F0F44917BAD33B1DC339173", "startup_urls": "E10CAB957EB9FCE3423E7E840F69F98A2B2E37BC1EDA325992678691E931212B"}}, "super_mac": "E1ED03BDAA0EC47347B5C774F4A7A5F17AB30BD6357C8B079369B693F60F14A3"}}