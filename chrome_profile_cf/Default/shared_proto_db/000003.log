�f�5            �f�5            �f�5            �����           
37_DEFAULT_16vh (0R*
(Session.TotalDurationT<A�GO (0�/
'
%
   ?ChromeLowUserEngagementOther (�ƙ�10���zX          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther (�ƙ�10s ��           
37_DEFAULT_23�� (0RH
F1Omnibox.SuggestionUsed.ClientSummarizedResultTypeq/�v�g:` (08Ra_
DSELECT COUNT(id) FROM metrics WHERE metric_hash = '64BD7CCE5A95BF00'
���ʖ�����������dh�8
0.


  �?Low


  �@Medium

  �AHighNone (�ƙ�10^{&%�           
37_DEFAULT_27�� ( 0R=;
"%
wait_for_device_info_in_seconds60*SyncDeviceInfoh p�t
rpAndroidPhoneIosPhoneChrome
AndroidTablet	IosTabletDesktopOtherSyncedAndFirstDevice	NotSynced���= (�ƙ�10�n0`           
37_DEFAULT_45D-6 ( 0R
*random�


   ?ShowDontShow (�ƙ�10��#�          
37_DEFAULT_46�.� ( 0R$"*time_since_last_modified_secR" *time_since_last_active_secR'%*time_active_for_time_period_secR*num_times_activeR*local_tab_countR*session_tab_countR*visit_countR*
is_bookmarkedR*	is_pinnedR*is_in_tab_groupR*
is_in_clusterR*has_url_keyed_imageR*
has_app_idR*
platform_typeR*seen_count_last_dayR *activated_count_last_dayR *dismissed_count_last_dayR*seen_count_last_7_daysR#!*activated_count_last_7_daysR#!*dismissed_count_last_7_daysR*seen_count_last_30_daysR$"*activated_count_last_30_daysR$"*dismissed_count_last_30_daysR#!*same_time_group_visit_countR" *same_day_group_visit_countZ+
%
#
!MetadataWriter�NUD~ (0h�!
*
url_visit_resumption_ranker (�ƙ�10+:&6          37_DEFAULT_1001��� (0R+
)Sync.DeviceCount2�|u3҉К (0
E    R1
/Sync.DeviceCount2.Phone�?fX��C (0
E    R3
1Sync.DeviceCount2.Desktop��P�� (0
E    R2
0Sync.DeviceCount2.Tablet3�����_ (0
E    ��
��

  �?NoCrossDeviceUsage

   @CrossDeviceMobile

  @@CrossDeviceDesktop

  �@CrossDeviceTablet
"
  �@CrossDeviceMobileAndDesktop
!
  �@CrossDeviceMobileAndTablet
"
  �@CrossDeviceDesktopAndTablet
 
   ACrossDeviceAllDeviceTypes

  ACrossDeviceOtherNoCrossDeviceUsage (�ƙ�10?�L%a          37_DEFAULT_1004��� (0R.
,MobileBookmarkManagerOpen>V'�~�� (0R3
1NewTabPage.MostVisited.Clicked}�э�-� (0R2
0TabGroup.Created.OpenInNewTabJ	>��n�C (0R1
/Android.HistoryPage.OpenItemŧ��K��, (0R)
'MobileMenuRecentTabs{��D�� (0�.
&
$
   ?ResumeHeavyUserSegmentOther (�ƙ�10)�~XW	          37_DEFAULT_1007��� (0R>
<'PasswordManager.ManagePasswordsReferrer*�ML���t (08 R[
YAPasswordManager.ProfileStore.TotalAccountsHiRes3.WithScheme.Https�,��l�k (0
E    R<
:!PasswordManager.FillingAssistanceՑM�� (08 88R?
=(PasswordManager.SavedPasswordIsGenerated��u��� (08R<
:%PasswordManager.SaveUIDismissalReason���w�� (08R@
>%PasswordManager.SaveUIDismissalReason���w�� (08 88R@
>)IOS.CredentialExtension.IsEnabled.Startup��u�%	� (08�=
5
3
   ?PasswordManagerUserNot_PasswordManagerUser (�ƙ�10���0� 
          37_DEFAULT_1009��� ( 0R<:
	select 1;-
+Ȏ���������ї�Z�򖐩�����ɕԺ���R
	select 1;
��ۊ����R
	select 1;

��˷�����


   ?N/AOther (�ƙ�10Wz��1           	39_config
��؈��O�ԓ �ƙ�1W�v~           	39_configf
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1[i�>� 
          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����ˮ���           	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1A�;          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1
��՛�����ԓ �ƙ�1
���Åօ�C�ԓ �ƙ�1
�����Ӆ���ԓ �ƙ�1
��������_�ԓ �ƙ�1�'U�          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1
��՛�����ԓ �ƙ�1
���Åօ�C�ԓ �ƙ�1
�����Ӆ���ԓ �ƙ�1
��������_�ԓ �ƙ�1
�����������I �ƙ�1
�������I �ƙ�1
ʒ���қ�C��I �ƙ�1
���޾���,��I �ƙ�1
���������I �ƙ�1Ks��P          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1
��՛�����ԓ �ƙ�1
���Åօ�C�ԓ �ƙ�1
�����Ӆ���ԓ �ƙ�1
��������_�ԓ �ƙ�1
�����������I �ƙ�1
�������I �ƙ�1
ʒ���қ�C��I �ƙ�1
���޾���,��I �ƙ�1
���������I �ƙ�1
������t�ԓ �ƙ�1
��������k�ԓ �ƙ�1
գ��������ԓ �ƙ�1
��ר�ٳ���ԓ �ƙ�1
ෛ�������ԓ �ƙ�1
������Ʉ��ԓ �ƙ�1��O޾          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1
��՛�����ԓ �ƙ�1
���Åօ�C�ԓ �ƙ�1
�����Ӆ���ԓ �ƙ�1
��������_�ԓ �ƙ�1
�����������I �ƙ�1
�������I �ƙ�1
ʒ���қ�C��I �ƙ�1
���޾���,��I �ƙ�1
���������I �ƙ�1
������t�ԓ �ƙ�1
��������k�ԓ �ƙ�1
գ��������ԓ �ƙ�1
��ר�ٳ���ԓ �ƙ�1
ෛ�������ԓ �ƙ�1
������Ʉ��ԓ �ƙ�1
"��ї�Z�ԓ �ƙ�1(Ȏ�������
#�򖐩�����ԓ �ƙ�1(Ȏ�������
#�ɕԺ����ԓ �ƙ�1(Ȏ�����������           20_1_1
1��'�            ����       .   4_EsbDownloadRowPromo
EsbDownloadRowPromo��4_IPH_BatterySaverMode
IPH_BatterySaverMode��4_IPH_CompanionSidePanel
IPH_CompanionSidePanel��$4_IPH_CompanionSidePanelRegionSearch(
"IPH_CompanionSidePanelRegionSearch��4_IPH_DesktopCustomizeChrome 
IPH_DesktopCustomizeChrome��4_IPH_DiscardRing
IPH_DiscardRing��4_IPH_DownloadEsbPromo
IPH_DownloadEsbPromo��/4_IPH_ExplicitBrowserSigninPreferenceRemembered3
-IPH_ExplicitBrowserSigninPreferenceRemembered��4_IPH_HistorySearch
IPH_HistorySearch��&4_IPH_FocusHelpBubbleScreenReaderPromo*
$IPH_FocusHelpBubbleScreenReaderPromo��4_IPH_GMCCastStartStop
IPH_GMCCastStartStop��4_IPH_GMCLocalMediaCasting
IPH_GMCLocalMediaCasting��4_IPH_HighEfficiencyMode
IPH_HighEfficiencyMode��4_IPH_LiveCaption
IPH_LiveCaption��(4_IPH_PasswordsManagementBubbleAfterSave,
&IPH_PasswordsManagementBubbleAfterSave��+4_IPH_PasswordsManagementBubbleDuringSignin/
)IPH_PasswordsManagementBubbleDuringSignin��"4_IPH_PasswordsWebAppProfileSwitch&
 IPH_PasswordsWebAppProfileSwitch��4_IPH_PasswordSharingFeature 
IPH_PasswordSharingFeature��4_IPH_PdfSearchifyFeature
IPH_PdfSearchifyFeature��*4_IPH_PerformanceInterventionDialogFeature.
(IPH_PerformanceInterventionDialogFeature��-4_IPH_PriceInsightsPageActionIconLabelFeature1
+IPH_PriceInsightsPageActionIconLabelFeature��&4_IPH_PriceTrackingEmailConsentFeature*
$IPH_PriceTrackingEmailConsentFeature��-4_IPH_PriceTrackingPageActionIconLabelFeature1
+IPH_PriceTrackingPageActionIconLabelFeature��4_IPH_ReadingModeSidePanel
IPH_ReadingModeSidePanel��4_IPH_ShoppingCollectionFeature#
IPH_ShoppingCollectionFeature��%4_IPH_SidePanelGenericPinnableFeature)
#IPH_SidePanelGenericPinnableFeature��)4_IPH_SidePanelLensOverlayPinnableFeature-
'IPH_SidePanelLensOverlayPinnableFeature��14_IPH_SidePanelLensOverlayPinnableFollowupFeature5
/IPH_SidePanelLensOverlayPinnableFollowupFeature��4_IPH_SignoutWebIntercept
IPH_SignoutWebIntercept��4_IPH_TabGroupsSaveV2Intro
IPH_TabGroupsSaveV2Intro��4_IPH_TabGroupsSaveV2CloseGroup#
IPH_TabGroupsSaveV2CloseGroup��4_IPH_ProfileSwitch
IPH_ProfileSwitch��4_IPH_PriceTrackingInSidePanel"
IPH_PriceTrackingInSidePanel��4_IPH_PwaQuietNotification
IPH_PwaQuietNotification��4_IPH_AutofillAiOptIn
IPH_AutofillAiOptIn��'4_IPH_AutofillBnplAffirmOrZipSuggestion+
%IPH_AutofillBnplAffirmOrZipSuggestion��.4_IPH_AutofillExternalAccountProfileSuggestion2
,IPH_AutofillExternalAccountProfileSuggestion��&4_IPH_AutofillVirtualCardCVCSuggestion*
$IPH_AutofillVirtualCardCVCSuggestion��#4_IPH_AutofillVirtualCardSuggestion'
!IPH_AutofillVirtualCardSuggestion��4_IPH_CookieControls
IPH_CookieControls��$4_IPH_DesktopPWAsLinkCapturingLaunch(
"IPH_DesktopPWAsLinkCapturingLaunch��,4_IPH_DesktopPWAsLinkCapturingLaunchAppInTab0
*IPH_DesktopPWAsLinkCapturingLaunchAppInTab��!4_IPH_SupervisedUserProfileSignin%
IPH_SupervisedUserProfileSignin��4_IPH_iOSPasswordPromoDesktop!
IPH_iOSPasswordPromoDesktop��4_IPH_iOSAddressPromoDesktop 
IPH_iOSAddressPromoDesktop��4_IPH_iOSPaymentPromoDesktop 
IPH_iOSPaymentPromoDesktop���0���B          
37_DEFAULT_21�� ( 0RZ
XCCommerce.PriceDrops.ActiveTabNavigationComplete.IsProductDetailPagew�cG$ؔ (08R9
7$Autofill_PolledCreditCardSuggestions��c�vP� (0R>
<$IOS.ParcelTracking.Tracked.AutoTrack-����|� (0
E    R" *TotalShoppingBookmarkCount�$


   ?ShoppingUserOther6
    �ȅ���$


   ?ShoppingUserOther  (�ƙ�10�Jtz: C          #38_h       OG�A<T   �c�   �c�
��q&E)$D          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION�.aWGE          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��F          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2΢�EG          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSg�4I(H          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING���&I          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGz�{u?J          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0( 2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONl�st$K          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION7d�3$L          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@ H Pϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION|�H�$M          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@ HPϟ�/X ` p x � ��X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTIONF�+GN          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�J��GO          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@ H Pϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�$X�GP          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@ HPϟ�/X ` p x � ��i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS�O�=Q          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2{�,~ER          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS+a*(S          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING3Wk�&T          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGP���?U          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION-���V          021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     H�+�BW          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@ HPϟ�/X ` p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH89G3_u0LBakfRE2EPPkQBsb8LKkmzT21v8WBYzRyZjjR6XcJgVcc4UeK4BLEtvkIakV accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:15 GMT expires:Wed, 16 Jul 2025 04:01:15 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION���X          021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 1 7 4 6 . c r d o w n l o a d   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x ����������������� �� �� � � � � ����������������������


     �c�LY          021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 1 7 4 6 . c r d o w n l o a d   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x������������������� �� �� � � � � ����������������������


     021_download,edf2fd49-434d-4da2-ad28-5286bf81436f�
�
$edf2fd49-434d-4da2-ad28-5286bf81436f
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     5'u[          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8__piSLaZAJQlUeBlC9UF9hqD_IMYRl2W9O3k9rlOqwND1RWOwMSJxkaOCoSSmGoNk vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:15 GMT expires:Wed, 16 Jul 2025 04:01:15 GMT accept-ranges:bytes x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS����\          021_download,edf2fd49-434d-4da2-ad28-5286bf81436f�
�
$edf2fd49-434d-4da2-ad28-5286bf81436f
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  4 4 1 0 6 1 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e d f 2 f d 4 9 - 4 3 4 d - 4 d a 2 - a d 2 8 - 5 2 8 6 b f 8 1 4 3 6 f   x ����������������� �� �� � � � � ����������������������


     t�F�]          021_download,edf2fd49-434d-4da2-ad28-5286bf81436f�
�
$edf2fd49-434d-4da2-ad28-5286bf81436f
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e d f 2 f d 4 9 - 4 3 4 d - 4 d a 2 - a d 2 8 - 5 2 8 6 b f 8 1 4 3 6 f   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e d f 2 f d 4 9 - 4 3 4 d - 4 d a 2 - a d 2 8 - 5 2 8 6 b f 8 1 4 3 6 f   xڎ�������ױ���� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     ��r�^          &9_edf2fd49-434d-4da2-ad28-5286bf81436f�	$edf2fd49-434d-4da2-ad28-5286bf81436f��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\edf2fd49-434d-4da2-ad28-5286bf81436f8������@Г����HPϟ�/Xڎ`Г����p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8__piSLaZAJQlUeBlC9UF9hqD_IMYRl2W9O3k9rlOqwND1RWOwMSJxkaOCoSSmGoNk vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:15 GMT expires:Wed, 16 Jul 2025 04:01:15 GMT accept-ranges:bytes x-goog-hash:crc32c=MvWUXQ== content-length:18266 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���i
.PredictionModelOptimizationTargetCustomDataKey7OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS˩��_          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@ H Pϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2$�r.`          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@ HPϟ�/X ` p x � ��T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2(i<�a          021_download,edf2fd49-434d-4da2-ad28-5286bf81436f�
�
$edf2fd49-434d-4da2-ad28-5286bf81436f
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745312779&target=OPTIMIZATION_TARGET_NOTIFICATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J PڎZ	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e d f 2 f d 4 9 - 4 3 4 d - 4 d a 2 - a d 2 8 - 5 2 8 6 b f 8 1 4 3 6 f   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ e d f 2 f d 4 9 - 4 3 4 d - 4 d a 2 - a d 2 8 - 5 2 8 6 b f 8 1 4 3 6 f   xڎ�������ױ���� ;��a�L�qKa魏P�{d�P+ f#�L���AZܠ��� � � � � ����������������������


     �8�> b           021_download,edf2fd49-434d-4da2-ad28-5286bf81436f���+c          021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 1 7 4 6 . c r d o w n l o a d   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x������������������ �� �� � � � � ����������������������


     �*C��e          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@ HPϟ�/X ` p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH88PDSJ4089Spl_Y91cGPmBLg31F9BoBFDS7p72vM5VXsnJ3tXgkXBxdqolamj0lRKw vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:16 GMT�J& expires:Wed, 16 Jul 2025 04:01:16 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V2F���
f          021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 7 8 2 1 8 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   x ���������������� �� �� � � � � ����������������������


     021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   g   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 1 7 4 6 . c r d o w n l o a d   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x������������������ �!��Q�k���֧*��:{`B��aOW�+V�@�� �� � � � � ����������������������


     q7x�h          021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     ��(Ri          &9_a767a57e-e9f2-464a-af01-d2b1ce27ac94�	$a767a57e-e9f2-464a-af01-d2b1ce27ac94��������  ( "�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\a767a57e-e9f2-464a-af01-d2b1ce27ac948������@������HPϟ�/X�`������p x �shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH89G3_u0LBakfRE2EPPkQBsb8LKkmzT21v8WBYzRyZjjR6XcJgVcc4UeK4BLEtvkIakV accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:15 GMT expires:Wed, 16 Jul 2025 04:01:15 GMT x-goog-hash:crc32c=1IKXVw== content-length:265059 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���X
.PredictionModelOptimizationTargetCustomDataKey&OPTIMIZATION_TARGET_LANGUAGE_DETECTION��{�Ej          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@ H Pϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSl�iEk          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@ HPϟ�/X ` p x � ��h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�¥��l          021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac94�
�
$a767a57e-e9f2-464a-af01-d2b1ce27ac94
��������"�
shttps://optimizationguide-pa.googleapis.com/downloads?name=1679317318&target=OPTIMIZATION_TARGET_LANGUAGE_DETECTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ a 7 6 7 a 5 7 e - e 9 f 2 - 4 6 4 a - a f 0 1 - d 2 b 1 c e 2 7 a c 9 4   x�������������� �!��Q�k���֧*��:{`B��aOW�+V�@���� � � � � ����������������������


     wpv~> m           021_download,a767a57e-e9f2-464a-af01-d2b1ce27ac949��<n          021_download,0823ce6f-fcbf-43bc-8e8d-8254b727e33b�
�
$0823ce6f-fcbf-43bc-8e8d-8254b727e33b
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x �ú�������������� �� � � � � � � ����������������������


     021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 7 8 2 1 8 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   x��?���������������� �� �� � � � � ����������������������


     U�q�zp          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_7MVaRlCBAdjMC2Bps8LMSLA9xMeC0q4UhN_g6WhY0VgNeZJ1vVDa6Oy2WENa-8o_IABdluA4 vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:16 GMT expires:Wed, 16 Jul 2025 04:01:16 GMT accept-ranges:bytes x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS�8��Dq          021_download,0823ce6f-fcbf-43bc-8e8d-8254b727e33b�
�
$0823ce6f-fcbf-43bc-8e8d-8254b727e33b
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  4 0 2 6 6 7 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 8 2 3 c e 6 f - f c b f - 4 3 b c - 8 e 8 d - 8 2 5 4 b 7 2 7 e 3 3 b   x���ú�������������� \�S�]���s�b0��l?�D}�F��.�"&�r�� �� � � � � ����������������������


     021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  7 7 8 2 1 8 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   x������������������� {�?�Ǩ�O�ƺ����f.RMH����=��� �� � � � � ����������������������


     d@^N�s          021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   x��������������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     4���t          &9_4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�	$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c��������  ( "�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2GET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\4e4a30b7-8da4-4efc-a5ba-f6c755775e2c8������@����HPϟ�/X���`����p x �fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2��HTTP/1.1 200 x-guploader-uploadid:ABgVH88PDSJ4089Spl_Y91cGPmBLg31F9BoBFDS7p72vM5VXsnJ3tXgkXBxdqolamj0lRKw vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:16 GMT expires:Wed, 16 Jul 2025 04:01:16 GMT accept-ranges:bytes x-goog-hash:crc32c=ENpSOA== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���T
.PredictionModelOptimizationTargetCustomDataKey"OPTIMIZATION_TARGET_PAGE_TOPICS_V22��(u          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@ H Pϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��Jd(v          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@ HPϟ�/X ` p x � ��Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��)�w          021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c�
�
$4e4a30b7-8da4-4efc-a5ba-f6c755775e2c
��������"�
fhttps://optimizationguide-pa.googleapis.com/downloads?name=5&target=OPTIMIZATION_TARGET_PAGE_TOPICS_V2 " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P���Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 4 e 4 a 3 0 b 7 - 8 d a 4 - 4 e f c - a 5 b a - f 6 c 7 5 5 7 7 5 e 2 c   x��������������� {�?�Ǩ�O�ƺ����f.RMH����=����� � � � � ����������������������


     ���I> x           021_download,4e4a30b7-8da4-4efc-a5ba-f6c755775e2c���y          021_download,0823ce6f-fcbf-43bc-8e8d-8254b727e33b�
�
$0823ce6f-fcbf-43bc-8e8d-8254b727e33b
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 8 2 3 c e 6 f - f c b f - 4 3 b c - 8 e 8 d - 8 2 5 4 b 7 2 7 e 3 3 b   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 8 2 3 c e 6 f - f c b f - 4 3 b c - 8 e 8 d - 8 2 5 4 b 7 2 7 e 3 3 b   x���ú���������� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     "���z          &9_0823ce6f-fcbf-43bc-8e8d-8254b727e33b�	$0823ce6f-fcbf-43bc-8e8d-8254b727e33b��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONSGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\0823ce6f-fcbf-43bc-8e8d-8254b727e33b8������@������HPϟ�/X��`������p x ��https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_7MVaRlCBAdjMC2Bps8LMSLA9xMeC0q4UhN_g6WhY0VgNeZJ1vVDa6Oy2WENa-8o_IABdluA4 vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:16 GMT expires:Wed, 16 Jul 2025 04:01:16 GMT accept-ranges:bytes x-goog-hash:crc32c=jy4DOA== content-length:18806 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���h
.PredictionModelOptimizationTargetCustomDataKey6OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS���&{          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@ H Pϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��G_&|          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@ HPϟ�/X ` p x � ��Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�4�}          021_download,0823ce6f-fcbf-43bc-8e8d-8254b727e33b�
�
$0823ce6f-fcbf-43bc-8e8d-8254b727e33b
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=1745311339&target=OPTIMIZATION_TARGET_GEOLOCATION_PERMISSION_PREDICTIONS " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 8 2 3 c e 6 f - f c b f - 4 3 b c - 8 e 8 d - 8 2 5 4 b 7 2 7 e 3 3 b   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 0 8 2 3 c e 6 f - f c b f - 4 3 b c - 8 e 8 d - 8 2 5 4 b 7 2 7 e 3 3 b   x���ú���������� \�S�]���s�b0��l?�D}�F��.�"&�r���� � � � � ����������������������


     b�q> ~           021_download,0823ce6f-fcbf-43bc-8e8d-8254b727e33b''���          021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�
�
$c4ed45dd-c175-4458-9f35-d7e95544d83a
��������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     M��1�          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@ HPϟ�/X ` p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_HTZFq_2I0xYuK7sWt1DE-EI_-tL2VSnxdcyMbcD2ZJAqR-HaDd5y0nEIt2_9HdhM date:Tue, 15 Jul 2025 04:01:17 GMT expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�����          021_download,ce2d9803-1f66-4b84-9c9c-09118aabba25�
�
$ce2d9803-1f66-4b84-9c9c-09118aabba25
��������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj       r       x ���������������� �� � � � � � � ����������������������


     ��VJ�          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@ HPϟ�/X ` p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_Yr34sCFeEraGBsARK6moJ9A3Nq-KauL8U9hAMNS9oFHYLjxks7z_QxfLIobyeCsUf7hcExTo expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:17 GMT x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��k��          021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�
�
$c4ed45dd-c175-4458-9f35-d7e95544d83a
��������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  5 6 2 3 1 4 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   x ���������������� �� �� � � � � ����������������������


     ��8��          021_download,ce2d9803-1f66-4b84-9c9c-09118aabba25�
�
$ce2d9803-1f66-4b84-9c9c-09118aabba25
��������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  4 7 8 4 3 3 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c e 2 d 9 8 0 3 - 1 f 6 6 - 4 b 8 4 - 9 c 9 c - 0 9 1 1 8 a a b b a 2 5   x�&���������������� ��4�z��2���K"�\kd�ɔ\AZWQ�� �� � � � � ����������������������


     �$$f��          021_download,ce2d9803-1f66-4b84-9c9c-09118aabba25�
�
$ce2d9803-1f66-4b84-9c9c-09118aabba25
��������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c e 2 d 9 8 0 3 - 1 f 6 6 - 4 b 8 4 - 9 c 9 c - 0 9 1 1 8 a a b b a 2 5   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c e 2 d 9 8 0 3 - 1 f 6 6 - 4 b 8 4 - 9 c 9 c - 0 9 1 1 8 a a b b a 2 5   x�&������������ ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     ��`�Y�          &9_ce2d9803-1f66-4b84-9c9c-09118aabba25�	$ce2d9803-1f66-4b84-9c9c-09118aabba25��������  ( "�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\ce2d9803-1f66-4b84-9c9c-09118aabba258������@�ɗ���HPϟ�/X�&`�ɗ���p x �thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_Yr34sCFeEraGBsARK6moJ9A3Nq-KauL8U9hAMNS9oFHYLjxks7z_QxfLIobyeCsUf7hcExTo expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 date:Tue, 15 Jul 2025 04:01:17 GMT x-goog-hash:crc32c=0qF+5Q== content-length:4879 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Y
.PredictionModelOptimizationTargetCustomDataKey'OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING�)�P?�          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@ H Pϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION!lU�?�          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@ HPϟ�/X ` p x � ��d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION�H���          021_download,ce2d9803-1f66-4b84-9c9c-09118aabba25�
�
$ce2d9803-1f66-4b84-9c9c-09118aabba25
��������"�
thttps://optimizationguide-pa.googleapis.com/downloads?name=1696268326&target=OPTIMIZATION_TARGET_OMNIBOX_URL_SCORING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�&Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c e 2 d 9 8 0 3 - 1 f 6 6 - 4 b 8 4 - 9 c 9 c - 0 9 1 1 8 a a b b a 2 5   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c e 2 d 9 8 0 3 - 1 f 6 6 - 4 b 8 4 - 9 c 9 c - 0 9 1 1 8 a a b b a 2 5   x�&������������ ��4�z��2���K"�\kd�ɔ\AZWQ���� � � � � ����������������������


     ��R> �           021_download,ce2d9803-1f66-4b84-9c9c-09118aabba25D����          021_download,1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�
�
$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj       r       x ����������������� �� � � � � � � ����������������������


     ��m?s�          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@ HPϟ�/X ` p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88niX32z9koYvVN4VfuB5OlVQBPfZwS1sALBZbckoeQrx45nE0vt0DuSlyuIEWssvyQf063-9g date:Tue, 15 Jul 2025 04:01:17 GMT expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONs|-CQ�          021_download,1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�
�
$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  8 3 8 1 6 4 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 1 d 5 e e 3 - f c 7 7 - 4 4 5 e - a e b 5 - c 2 9 3 9 a 2 e 0 7 1 e   x������������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ� �� � � � � ����������������������


     021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�
�
$c4ed45dd-c175-4458-9f35-d7e95544d83a
��������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   h   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ *gnx��  5 6 2 3 1 4 . c r d o w n l o a d r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   x�֧���������������� 'Ok-����(���e��e�Vs`�x�lO�Rla�� �� � � � � ����������������������


     M���          021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�
�
$c4ed45dd-c175-4458-9f35-d7e95544d83a
��������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \H����  c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   x�֧������������ 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     X#B�          &9_c4ed45dd-c175-4458-9f35-d7e95544d83a�	$c4ed45dd-c175-4458-9f35-d7e95544d83a��������  ( "�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHINGGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\c4ed45dd-c175-4458-9f35-d7e95544d83a8������@�����HPϟ�/X�֧`�����p x �uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING��HTTP/1.1 200 x-guploader-uploadid:ABgVH8_HTZFq_2I0xYuK7sWt1DE-EI_-tL2VSnxdcyMbcD2ZJAqR-HaDd5y0nEIt2_9HdhM date:Tue, 15 Jul 2025 04:01:17 GMT expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=RyXJNg== server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���Z
.PredictionModelOptimizationTargetCustomDataKey(OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING�f����          021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�
�
$c4ed45dd-c175-4458-9f35-d7e95544d83a
��������"�
uhttps://optimizationguide-pa.googleapis.com/downloads?name=1750777447&target=OPTIMIZATION_TARGET_CLIENT_SIDE_PHISHING " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P�֧Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ c 4 e d 4 5 d d - c 1 7 5 - 4 4 5 8 - 9 f 3 5 - d 7 e 9 5 5 4 4 d 8 3 a   x�֧������������ 'Ok-����(���e��e�Vs`�x�lO�Rla���� � � � � ����������������������


     Ե��> �           021_download,c4ed45dd-c175-4458-9f35-d7e95544d83a�zR���          021_download,1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�
�
$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 1 d 5 e e 3 - f c 7 7 - 4 4 5 e - a e b 5 - c 2 9 3 9 a 2 e 0 7 1 e   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 1 d 5 e e 3 - f c 7 7 - 4 4 5 e - a e b 5 - c 2 9 3 9 a 2 e 0 7 1 e   x��������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     �װփ�          &9_1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�	$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e��������  ( "�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTIONGET9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhE  ( 0(2wC:\Users\<USER>\PyCharmMiscProject\chrome_profile_cf\Default\Download Service\Files\1b1d5ee3-fc77-445e-aeb5-c2939a2e071e8������@�Ͳ���HPϟ�/X��`�Ͳ���p x ��https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��HTTP/1.1 200 x-guploader-uploadid:ABgVH88niX32z9koYvVN4VfuB5OlVQBPfZwS1sALBZbckoeQrx45nE0vt0DuSlyuIEWssvyQf063-9g date:Tue, 15 Jul 2025 04:01:17 GMT expires:Wed, 16 Jul 2025 04:01:17 GMT accept-ranges:bytes vary:X-Goog-Api-Key cache-control:public, max-age=86400 x-goog-hash:crc32c=sGxD1w== content-length:118577 server:UploadServer content-type:text/html; charset=UTF-8 alt-svc:h3=":443"; ma=2592000,h3-29=":443"; ma=2592000  ���d
.PredictionModelOptimizationTargetCustomDataKey2OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION��^���          021_download,1b1d5ee3-fc77-445e-aeb5-c2939a2e071e�
�
$1b1d5ee3-fc77-445e-aeb5-c2939a2e071e
��������"�
�https://optimizationguide-pa.googleapis.com/downloads?name=************&target=OPTIMIZATION_TARGET_SEGMENTATION_COMPOSE_PROMOTION " * 0 :9
X-Goog-Api-Key'AIzaSyA2KlwBX3mkFo30om9LUFYQhpqLoa_BNhEB J P��Z	text/htmlb	text/htmlj��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 1 d 5 e e 3 - f c 7 7 - 4 4 5 e - a e b 5 - c 2 9 3 9 a 2 e 0 7 1 e   r��   w   C : \ U s e r s \ A d m i n \ P y C h a r m M i s c P r o j e c t \ c h r o m e _ p r o f i l e _ c f \ D e f a u l t \ D o w n l o a d   S e r v i c e \ F i l e s \ 1 b 1 d 5 e e 3 - f c 7 7 - 4 4 5 e - a e b 5 - c 2 9 3 9 a 2 e 0 7 1 e   x��������������� vH0z
Ђ��	ZWE9FZ���=L�ErL4�O�ߠ��� � � � � ����������������������


     {
�H> �           021_download,1b1d5ee3-fc77-445e-aeb5-c2939a2e071euMDVF�          	39_config�
��؈��O�ԓ �ƙ�1
����Ą���ԓ �ƙ�1
�����ٝ���ԓ �ƙ�1
�����ؿ���ԓ �ƙ�1
�ހ���`�ԓ �ƙ�1
"�������d�ԓ �ƙ�1(���ʖ����
ۯ��Њ���ԓ �ƙ�1
��՛�����ԓ �ƙ�1
���Åօ�C�ԓ �ƙ�1
�����Ӆ���ԓ �ƙ�1
��������_�ԓ �ƙ�1
�����������I �ƙ�1
�������I �ƙ�1
ʒ���қ�C��I �ƙ�1
���޾���,��I �ƙ�1
���������I �ƙ�1
������t�ԓ �ƙ�1
��������k�ԓ �ƙ�1
գ��������ԓ �ƙ�1
��ר�ٳ���ԓ �ƙ�1
ෛ�������ԓ �ƙ�1
������Ʉ��ԓ �ƙ�1
"��ї�Z�ԓ �ƙ�1(Ȏ�������
#�򖐩�����ԓ �ƙ�1(Ȏ�������
#�ɕԺ����ԓ �ƙ�1(Ȏ�������
!������վN�ԓ �Ǚ�1(��������'
"��������ԓ �Ǚ�1(��������'
"��ڀ����ԓ �Ǚ�1(��������'
!���䍟��B�ԓ �Ǚ�1(��������'
"����̂呮�ԓ �Ǚ�1(��������'
#������Ơ��ԓ �Ǚ�1(��袺ص��
#�풠�����ԓ �Ǚ�1(��袺ص��
!�����Ù��ԓ �Ǚ�1(�ٴ�ڥ�7
!���������ԓ �Ǚ�1(�ٴ�ڥ�7
!��ޚ�đ�y�ԓ �Ǚ�1(�ٴ�ڥ�7
!������ڷu�ԓ �Ǚ�1(�ٴ�ڥ�7}�r�d�          37_45�6-�6 (0R*field_form_control_typeR*total_field_countR*multiline_field_countR*time_spent_on_pageR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN�������"
words_writtenR��
�WITH event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ���"editing_timeR��
uSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ���"editing_sessionsR��
vSELECT COUNT(metric_value) FROM metrics WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ���"long_editing_sessionsR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;
��������'��ڀ��� "
nameorigin"editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(metric_value) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;
��������'��ڀ��� "
nameorigin"long_editing_sessions_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 45;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids_for_field AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'AE239414CC02813B' AND metrics.metric_value = ? ), event_ids_for_form AS ( SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = '42D464F8DC98BC83' AND metrics.metric_value = ? ) SELECT COUNT(metric_value) FROM metrics INNER JOIN event_ids_for_field ON metrics.event_id = event_ids_for_field.event_id INNER JOIN event_ids_for_form ON metrics.event_id = event_ids_for_form.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5' AND metrics.metric_value > 120;,
*��������'���䍟��B����̂呮��ڀ��� "
nameorigin#"
namefield_signature""
nameform_signature"long_editing_sessions_on_fieldR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = 'CE71BF280B4EB4B5';"
 ��������'���������ڀ��� "
nameorigin"editing_time_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0), event_ids AS (SELECT DISTINCT event_id FROM metrics WHERE metrics.metric_hash = 'CCC33F79ECE7363D' AND metrics.metric_value IN (0, 14)) SELECT IFNULL(SUM(metric_value), 0) FROM metrics INNER JOIN event_ids ON metrics.event_id = event_ids.event_id INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '4E7D579594EB8315';!
��������'������վN������� "
nameorigin"words_typed_on_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT COUNT(id) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d "
nameorigin"page_loads_on_siteRvt
MSELECT COUNT(id) FROM metrics WHERE metrics.metric_hash = '64BD7CCE5A95BF00';
���ʖ�����������d"
page_loadsR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'AD411B741D0DA012' AND metrics.metric_value > 0;
��袺ص��������Ơ�"compose_session_countR��
�SELECT COUNT(DISTINCT CAST((event_timestamp / 1000000 / 60 / 10) AS int)) FROM metrics WHERE metrics.metric_hash = 'B4CFE8741404B691' AND metrics.metric_value > 0;
��袺ص���풠����"compose_insert_session_countR��
gSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù�"nudge_showsR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7��������"nudge_clicksR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '79964621D357AB88';
�ٴ�ڥ�7��ޚ�đ�y"nudge_site_disablesR��
hSELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics WHERE metrics.metric_hash = '756F6A466879157E';
�ٴ�ڥ�7������ڷu"nudge_global_disablesR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '534661B278B11BD';
�ٴ�ڥ�7�����Ù� "
nameorigin"nudge_shows_siteR��
�WITH on_site_urlids AS (SELECT DISTINCT url_id FROM urls WHERE instr(url, ?) > 0) SELECT IFNULL(SUM(metrics.metric_value), 0) FROM metrics INNER JOIN on_site_urlids ON metrics.url_id = on_site_urlids.url_id WHERE metrics.metric_hash = '19E16122849E343B';
�ٴ�ڥ�7�������� "
nameorigin"nudge_clicks_site�!


  @?ShowDontShow ����(�Ǚ�10�4X9 �          #38_h       k�l��,�   �c�   �c�
 �q�i��: �          #38_h       OG�A<T   �c�   �c�
��q)���: �          #38_h       OG�A<T   �c�   �c�
��q�: �          #38_h       OG�A<T   �c�   �c�
�I�qM7�O �           M7�O �           M7�O �           M7�O �           M7�O �           �
: �          #38_h       OG�A<T   �dJ   �dJ
��r