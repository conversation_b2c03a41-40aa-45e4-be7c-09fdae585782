#!/usr/bin/env python3
"""
Main Entry Point for Web Crawling Task Management System
Handles initialization and resolves circular import issues
"""

import os
import sys
import json
import logging
import asyncio
from pathlib import Path

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import core modules
from task_management_api import app, TaskManager, TaskStatus
from task_browser_manager import TaskBrowserManager
from task_execution_engine import TaskExecutionEngine, set_task_manager_classes

# Set up the circular import resolution
set_task_manager_classes(TaskManager, TaskStatus)

# Global instances
task_manager = TaskManager()
execution_engine = None


def load_configuration():
    """Load system configuration"""
    config_files = ['task_system_config.json', 'crawler_config.json']
    config = {}
    
    for config_file in config_files:
        if os.path.exists(config_file):
            try:
                with open(config_file, 'r') as f:
                    file_config = json.load(f)
                    config.update(file_config)
                print(f"✅ Loaded configuration from {config_file}")
            except Exception as e:
                print(f"⚠️  Error loading {config_file}: {e}")
        else:
            print(f"⚠️  Configuration file {config_file} not found")
    
    return config


def setup_logging(config):
    """Setup logging configuration"""
    log_config = config.get('logging', {})
    
    logging.basicConfig(
        level=getattr(logging, log_config.get('level', 'INFO')),
        format=log_config.get('format', 
            '%(asctime)s - %(name)s - %(levelname)s - [%(threadName)s] - %(message)s'),
        handlers=[
            logging.FileHandler(log_config.get('file', 'task_system.log'), encoding='utf-8'),
            logging.StreamHandler(sys.stdout) if log_config.get('console_output', True) else None
        ]
    )
    
    # Set specific logger levels
    logging.getLogger('uvicorn').setLevel(logging.INFO)
    logging.getLogger('fastapi').setLevel(logging.INFO)
    logging.getLogger('DrissionPage').setLevel(logging.WARNING)
    
    return logging.getLogger('task_system_main')


def create_basic_crawler_config():
    """Create a basic crawler configuration"""
    class BasicCrawlerConfig:
        def __init__(self):
            self.base_url = "https://lowendtalk.com/"
            self.state_file = "task_system_state.json"
            self.results_file = "task_system_results.json"
            self.flash_sale_keywords = [
                "offer", "sale", "discount", "promo", "limited", "flash sale",
                "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth",
                "$/month", "$/yr", "price", "deal", "special", "promotion"
            ]
            self.headless = True
            self.page_load_delay = 3.0
            self.min_request_delay = 1.0
            self.max_request_delay = 3.0
            self.max_retries = 3
            self.redis_enabled = True
            self.redis_host = "localhost"
            self.redis_port = 6379
            self.redis_db = 0
        
        def load_from_file(self, path):
            """Load configuration from file"""
            if os.path.exists(path):
                try:
                    with open(path, 'r') as f:
                        config_data = json.load(f)
                        for key, value in config_data.items():
                            if hasattr(self, key):
                                setattr(self, key, value)
                except Exception as e:
                    print(f"Warning: Could not load config from {path}: {e}")
    
    return BasicCrawlerConfig()


async def initialize_system():
    """Initialize the task management system"""
    global execution_engine
    
    print("🔧 Initializing Task Management System...")
    
    # Load configuration
    config_data = load_configuration()
    
    # Setup logging
    logger = setup_logging(config_data)
    logger.info("Starting Task Management System initialization")
    
    # Create crawler configuration
    crawler_config = create_basic_crawler_config()
    crawler_config.load_from_file("crawler_config.json")
    
    # Initialize execution engine
    execution_engine = TaskExecutionEngine(crawler_config, task_manager)
    
    logger.info("Task Management System initialized successfully")
    print("✅ System initialization complete")


async def shutdown_system():
    """Shutdown the task management system"""
    global execution_engine
    
    print("🛑 Shutting down Task Management System...")
    
    if execution_engine:
        await execution_engine.stop_all_tasks()
        execution_engine.cleanup()
    
    print("✅ System shutdown complete")


# FastAPI event handlers
@app.on_event("startup")
async def startup_event():
    """FastAPI startup event"""
    await initialize_system()


@app.on_event("shutdown")
async def shutdown_event():
    """FastAPI shutdown event"""
    await shutdown_system()


def print_system_info():
    """Print system information"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║                    Web Crawling Task Management System                       ║
║                           Version 1.0.0                                     ║
╠══════════════════════════════════════════════════════════════════════════════╣
║  🚀 REST API for managing forum post crawling tasks                         ║
║  🌐 Multi-domain browser management with tab isolation                      ║
║  🔄 Real-time incremental comment processing                                ║
║  🤖 AI-powered flash sale and pre-sale chat detection                       ║
║  📊 Redis-based distributed locking and state management                    ║
╚══════════════════════════════════════════════════════════════════════════════╝

📡 API Endpoints:
  POST   /tasks                    - Create new crawling task
  GET    /tasks                    - List all tasks
  GET    /tasks/{id}               - Get specific task details
  PUT    /tasks/{id}/start         - Start monitoring a task
  PUT    /tasks/{id}/stop          - Stop monitoring a task
  DELETE /tasks/{id}               - Delete a task
  POST   /tasks/stop-all           - Stop all running tasks
  GET    /                         - API health check

🔧 Configuration:
  - Edit task_system_config.json for system settings
  - Edit crawler_config.json for crawler settings
  - Check logs in task_system.log

📖 Documentation:
  - Swagger UI: http://localhost:8000/docs
  - ReDoc: http://localhost:8000/redoc
  - README: TASK_SYSTEM_README.md
    """
    print(banner)


def main():
    """Main entry point"""
    import argparse
    import uvicorn
    
    parser = argparse.ArgumentParser(description='Web Crawling Task Management System')
    parser.add_argument('--host', default='0.0.0.0', help='Host to bind to')
    parser.add_argument('--port', type=int, default=8000, help='Port to bind to')
    parser.add_argument('--reload', action='store_true', help='Enable auto-reload for development')
    parser.add_argument('--workers', type=int, default=1, help='Number of worker processes')
    
    args = parser.parse_args()
    
    # Print system information
    print_system_info()
    
    # Check basic requirements
    try:
        import fastapi
        import uvicorn
        import pydantic
        print("✅ Core dependencies available")
    except ImportError as e:
        print(f"❌ Missing core dependency: {e}")
        print("Please install: pip install fastapi uvicorn pydantic")
        sys.exit(1)
    
    # Optional dependency checks
    optional_deps = {
        'DrissionPage': 'Browser automation',
        'redis': 'Distributed locking',
        'sqlite3': 'Task storage'
    }
    
    for dep, description in optional_deps.items():
        try:
            __import__(dep)
            print(f"✅ {description} available ({dep})")
        except ImportError:
            print(f"⚠️  {description} not available ({dep}) - some features may be limited")
    
    print(f"\n🚀 Starting server on {args.host}:{args.port}")
    print(f"📖 API documentation: http://{args.host}:{args.port}/docs")
    print("="*80)
    
    try:
        uvicorn.run(
            "task_system_main:app",
            host=args.host,
            port=args.port,
            reload=args.reload,
            workers=args.workers if not args.reload else 1,
            log_level="info"
        )
    except KeyboardInterrupt:
        print("\n🛑 Shutting down gracefully...")
    except Exception as e:
        print(f"❌ Server startup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
