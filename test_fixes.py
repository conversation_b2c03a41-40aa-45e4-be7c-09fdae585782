#!/usr/bin/env python3
"""
Test script to verify the fixes for the forum crawler
"""

import sys
import os
import time
import logging

# Add the current directory to the path so we can import the crawler
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from improved_forum_crawler import C<PERSON>lerConfig, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def test_configuration_loading():
    """Test that configuration loads correctly with proper timeout values"""
    print("Testing configuration loading...")
    
    # Test loading from file
    config = CrawlerConfig.from_file("crawler_config.json")
    
    print(f"Monitor timeout: {config.monitor_timeout}s (should be 120)")
    print(f"Worker timeout: {config.worker_timeout}s (should be 60)")
    print(f"Queue timeout: {config.queue_timeout}s (should be 60)")
    print(f"Refresh interval: {config.refresh_interval}s (should be 60)")
    print(f"Number of workers: {config.num_workers} (should be 3)")
    
    # Verify values
    assert config.monitor_timeout == 120, f"Expected 120, got {config.monitor_timeout}"
    assert config.worker_timeout == 60, f"Expected 60, got {config.worker_timeout}"
    assert config.queue_timeout == 60, f"Expected 60, got {config.queue_timeout}"
    assert config.refresh_interval == 60, f"Expected 60, got {config.refresh_interval}"
    assert config.num_workers == 3, f"Expected 3, got {config.num_workers}"
    
    print("✅ Configuration loading test PASSED")


def test_logging_without_unicode():
    """Test that logging works without Unicode encoding errors"""
    print("\nTesting logging without Unicode issues...")
    
    # Setup logging
    logger = CrawlerLogger.setup_logging("INFO", "test_fixes.log")
    
    # Test various log messages that previously caused issues
    test_messages = [
        "System Status - Monitor: OK, Workers: 3/3 active",
        "QUEUED post: Test Post... (Queue size: 1)",
        "QUEUE Status: 5 tasks waiting for processing",
        "FLASH SALE detected on page 2! Comment #45, Confidence: 0.85",
        "Worker thread ended after processing 10 tasks"
    ]
    
    try:
        for i, message in enumerate(test_messages):
            logger.info(f"Test {i+1}: {message}")
            print(f"✅ Logged: {message}")
        
        print("✅ Logging test PASSED - No Unicode errors")
        
    except UnicodeEncodeError as e:
        print(f"❌ Logging test FAILED - Unicode error: {e}")
        return False
    
    return True


def test_browser_options_fallback():
    """Test that browser options fallback works"""
    print("\nTesting browser options fallback...")
    
    try:
        from improved_forum_crawler import BrowserManager, CrawlerConfig, AntiDetectionManager
        
        config = CrawlerConfig()
        browser_manager = BrowserManager(config)
        
        # This should not crash even if ChromiumOptions API is different
        browser_manager.start_browser()
        print("✅ Browser started successfully")
        
        # Clean up
        browser_manager.stop_browser()
        print("✅ Browser stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Browser test failed: {e}")
        return False


def main():
    """Run all tests"""
    print("Forum Crawler Fixes Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 3
    
    try:
        # Test 1: Configuration loading
        test_configuration_loading()
        tests_passed += 1
        
        # Test 2: Logging without Unicode
        if test_logging_without_unicode():
            tests_passed += 1
        
        # Test 3: Browser options fallback
        if test_browser_options_fallback():
            tests_passed += 1
            
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()
    
    print("\n" + "=" * 50)
    print(f"Tests completed: {tests_passed}/{total_tests} passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests PASSED! The fixes are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the issues above.")


if __name__ == "__main__":
    main()
