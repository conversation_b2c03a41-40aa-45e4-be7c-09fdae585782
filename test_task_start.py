#!/usr/bin/env python3
"""Test task creation and starting functionality"""

import requests
import json
import time

API_BASE = "http://127.0.0.1:8001"

def test_task_start():
    print("🧪 Testing Task Start Functionality...")
    
    # Create a task
    print("\n1. Creating a test task...")
    task_data = {
        "post_url": "https://www.nodeseek.com/post-12345",
        "forum_domain": "nodeseek.com",
        "monitor_interval": 60,
        "ai_analysis_enabled": True
    }
    
    response = requests.post(f"{API_BASE}/tasks", json=task_data)
    print(f"   Create Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"   Error creating task: {response.text}")
        return
    
    task = response.json()
    task_id = task["id"]
    print(f"   Created task: {task_id}")
    print(f"   Task status: {task['status']}")
    
    # Try to start the task
    print(f"\n2. Starting task {task_id[:8]}...")
    response = requests.put(f"{API_BASE}/tasks/{task_id}/start")
    print(f"   Start Status: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print(f"   Response: {result.get('message', 'Success')}")
        
        # Wait a moment and check task status
        print("\n3. Checking task status after start...")
        time.sleep(3)
        
        response = requests.get(f"{API_BASE}/tasks/{task_id}")
        if response.status_code == 200:
            task_status = response.json()
            print(f"   Task Status: {task_status['status']}")
            print(f"   Started At: {task_status.get('started_at', 'Not set')}")
            print(f"   Last Check: {task_status.get('last_check_at', 'Not set')}")
        
        # Stop the task
        print(f"\n4. Stopping task {task_id[:8]}...")
        response = requests.put(f"{API_BASE}/tasks/{task_id}/stop")
        print(f"   Stop Status: {response.status_code}")
        if response.status_code == 200:
            print(f"   Task stopped successfully")
    else:
        print(f"   Error starting task: {response.text}")
    
    # Clean up
    print(f"\n5. Cleaning up task {task_id[:8]}...")
    response = requests.delete(f"{API_BASE}/tasks/{task_id}")
    print(f"   Delete Status: {response.status_code}")
    
    print("\n✅ Task start test completed!")

if __name__ == "__main__":
    test_task_start()
