#!/usr/bin/env python3
"""清理所有任务"""

import requests

API_BASE = "http://127.0.0.1:8001"

def cleanup_all_tasks():
    """清理所有任务"""
    try:
        # 获取所有任务
        response = requests.get(f"{API_BASE}/tasks")
        if response.status_code == 200:
            tasks = response.json()
            print(f"找到 {len(tasks)} 个任务")
            
            # 停止并删除所有任务
            for task in tasks:
                task_id = task["id"]
                print(f"清理任务: {task_id[:8]}...")
                
                # 停止任务
                requests.put(f"{API_BASE}/tasks/{task_id}/stop")
                
                # 删除任务
                requests.delete(f"{API_BASE}/tasks/{task_id}")
                
                print(f"✅ 任务 {task_id[:8]} 已清理")
            
            print("✅ 所有任务已清理完成")
        else:
            print(f"❌ 无法获取任务列表: {response.status_code}")
    
    except Exception as e:
        print(f"❌ 清理失败: {e}")

if __name__ == "__main__":
    cleanup_all_tasks()
