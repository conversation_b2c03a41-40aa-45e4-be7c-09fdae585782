#!/usr/bin/env python3
"""
Setup Script for Web Crawling Task Management System
Installs dependencies and configures the system
"""

import os
import sys
import json
import subprocess
import shutil
from pathlib import Path


def print_banner():
    """Print setup banner"""
    banner = """
╔══════════════════════════════════════════════════════════════════════════════╗
║              Web Crawling Task Management System - Setup                    ║
╚══════════════════════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_python_version():
    """Check Python version compatibility"""
    print("🐍 Checking Python version...")
    
    if sys.version_info < (3, 8):
        print("❌ Python 3.8+ is required")
        print(f"   Current version: {sys.version}")
        return False
    
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor} is compatible")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("📦 Installing dependencies...")
    
    requirements_files = [
        'task_system_requirements.txt',
        'crawler_requirements.txt'
    ]
    
    for req_file in requirements_files:
        if os.path.exists(req_file):
            print(f"   Installing from {req_file}...")
            try:
                subprocess.run([
                    sys.executable, '-m', 'pip', 'install', '-r', req_file
                ], check=True, capture_output=True, text=True)
                print(f"   ✅ Installed dependencies from {req_file}")
            except subprocess.CalledProcessError as e:
                print(f"   ⚠️  Some packages from {req_file} failed to install: {e}")
                print("   Continuing with available packages...")
        else:
            print(f"   ⚠️  {req_file} not found, skipping...")
    
    # Install core dependencies manually if requirements files are missing
    core_deps = ['fastapi', 'uvicorn[standard]', 'pydantic']
    print("   Installing core dependencies...")
    
    try:
        subprocess.run([
            sys.executable, '-m', 'pip', 'install'
        ] + core_deps, check=True, capture_output=True, text=True)
        print("   ✅ Core dependencies installed")
    except subprocess.CalledProcessError as e:
        print(f"   ❌ Failed to install core dependencies: {e}")
        return False
    
    return True


def create_configuration_files():
    """Create default configuration files"""
    print("⚙️  Creating configuration files...")
    
    # Create basic crawler config
    crawler_config = {
        "base_url": "https://lowendtalk.com/",
        "monitor_url": "https://lowendtalk.com/categories/offers",
        "state_file": "task_system_state.json",
        "results_file": "task_system_results.json",
        "flash_sale_keywords": [
            "offer", "sale", "discount", "promo", "limited", "flash sale",
            "gb", "tb", "nvme", "ssd", "ram", "cpu", "core", "ip", "bandwidth",
            "$/month", "$/yr", "price", "deal", "special", "promotion",
            "cheap", "budget", "affordable", "exclusive", "limited time"
        ],
        "num_workers": 5,
        "monitor_timeout": 90,
        "worker_timeout": 30,
        "queue_timeout": 30,
        "headless": True,
        "refresh_interval": 60,
        "page_load_delay": 3.0,
        "min_request_delay": 1.0,
        "max_request_delay": 3.0,
        "max_retries": 3,
        "redis_enabled": True,
        "redis_host": "localhost",
        "redis_port": 6379,
        "redis_db": 0
    }
    
    if not os.path.exists('crawler_config.json'):
        with open('crawler_config.json', 'w') as f:
            json.dump(crawler_config, f, indent=2)
        print("   ✅ Created crawler_config.json")
    else:
        print("   ℹ️  crawler_config.json already exists")
    
    # Create task system config if it doesn't exist
    if not os.path.exists('task_system_config.json'):
        print("   ⚠️  task_system_config.json not found")
        print("   Please copy from task_system_config.json or create manually")
    else:
        print("   ✅ task_system_config.json exists")


def check_optional_dependencies():
    """Check optional dependencies and provide installation guidance"""
    print("🔍 Checking optional dependencies...")
    
    optional_deps = {
        'DrissionPage': {
            'description': 'Browser automation',
            'install': 'pip install DrissionPage',
            'critical': True
        },
        'redis': {
            'description': 'Distributed locking',
            'install': 'pip install redis',
            'critical': False
        },
        'beautifulsoup4': {
            'description': 'HTML parsing',
            'install': 'pip install beautifulsoup4',
            'critical': False
        }
    }
    
    missing_critical = []
    
    for dep, info in optional_deps.items():
        try:
            __import__(dep)
            print(f"   ✅ {info['description']} available ({dep})")
        except ImportError:
            if info['critical']:
                missing_critical.append(dep)
                print(f"   ❌ {info['description']} missing ({dep}) - REQUIRED")
                print(f"      Install with: {info['install']}")
            else:
                print(f"   ⚠️  {info['description']} missing ({dep}) - optional")
                print(f"      Install with: {info['install']}")
    
    return len(missing_critical) == 0


def check_browser_availability():
    """Check if Chrome/Chromium is available"""
    print("🌐 Checking browser availability...")
    
    browsers = ['google-chrome', 'chromium-browser', 'chromium', 'chrome']
    
    for browser in browsers:
        if shutil.which(browser):
            print(f"   ✅ Found browser: {browser}")
            return True
    
    print("   ⚠️  Chrome/Chromium not found in PATH")
    print("   DrissionPage will attempt to download Chrome automatically")
    print("   Or install manually:")
    print("   - Ubuntu/Debian: sudo apt-get install chromium-browser")
    print("   - macOS: brew install --cask google-chrome")
    print("   - Windows: Download from https://www.google.com/chrome/")
    
    return False


def check_redis_availability():
    """Check if Redis is available"""
    print("🔗 Checking Redis availability...")
    
    try:
        import redis
        r = redis.Redis(host='localhost', port=6379, db=0, socket_timeout=2)
        r.ping()
        print("   ✅ Redis server is running")
        return True
    except ImportError:
        print("   ⚠️  Redis Python client not installed")
        print("      Install with: pip install redis")
        return False
    except Exception:
        print("   ⚠️  Redis server not running or not accessible")
        print("   Redis is optional but recommended for distributed locking")
        print("   Install Redis:")
        print("   - Ubuntu/Debian: sudo apt-get install redis-server")
        print("   - macOS: brew install redis")
        print("   - Windows: Download from https://redis.io/download")
        return False


def create_startup_scripts():
    """Create convenient startup scripts"""
    print("📝 Creating startup scripts...")
    
    # Create start script for Unix systems
    start_script_unix = """#!/bin/bash
# Start Web Crawling Task Management System

echo "Starting Web Crawling Task Management System..."
python3 task_system_main.py "$@"
"""
    
    with open('start_system.sh', 'w') as f:
        f.write(start_script_unix)
    
    # Make executable on Unix systems
    if os.name != 'nt':
        os.chmod('start_system.sh', 0o755)
        print("   ✅ Created start_system.sh")
    
    # Create start script for Windows
    start_script_windows = """@echo off
REM Start Web Crawling Task Management System

echo Starting Web Crawling Task Management System...
python task_system_main.py %*
"""
    
    with open('start_system.bat', 'w') as f:
        f.write(start_script_windows)
    
    print("   ✅ Created start_system.bat")


def print_next_steps():
    """Print next steps for the user"""
    next_steps = """
🎉 Setup Complete!

📋 Next Steps:
1. Review configuration files:
   - task_system_config.json (system settings)
   - crawler_config.json (crawler settings)

2. Start the system:
   - Unix/Linux/macOS: ./start_system.sh
   - Windows: start_system.bat
   - Or directly: python task_system_main.py

3. Access the API:
   - API Documentation: http://localhost:8000/docs
   - Alternative docs: http://localhost:8000/redoc
   - Health check: http://localhost:8000/

4. Test the system:
   - Run tests: python test_task_system.py
   - Create a task via API or web interface

5. Optional improvements:
   - Install and configure Redis for better performance
   - Install Chrome/Chromium for browser automation
   - Review and customize AI analysis keywords

📖 Documentation:
   - Read TASK_SYSTEM_README.md for detailed usage
   - Check logs in task_system.log for troubleshooting

🆘 Need Help?
   - Check the troubleshooting section in README
   - Review logs for error details
   - Ensure all dependencies are properly installed
    """
    print(next_steps)


def main():
    """Main setup function"""
    print_banner()
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        print("❌ Dependency installation failed")
        sys.exit(1)
    
    # Create configuration files
    create_configuration_files()
    
    # Check optional dependencies
    if not check_optional_dependencies():
        print("⚠️  Some critical dependencies are missing")
        print("   The system may not work properly without them")
    
    # Check browser availability
    check_browser_availability()
    
    # Check Redis availability
    check_redis_availability()
    
    # Create startup scripts
    create_startup_scripts()
    
    # Print next steps
    print_next_steps()


if __name__ == "__main__":
    main()
