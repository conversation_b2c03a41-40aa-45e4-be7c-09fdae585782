# Web Crawling Task Management System

A comprehensive REST API-based task scheduler for managing forum crawling tasks with real-time monitoring, AI-powered content analysis, and distributed browser management.

## 🚀 Features

### Core Architecture
- **REST API Task Scheduler**: Manage crawling tasks at individual forum post level
- **Multi-Domain Browser Management**: One browser instance per forum domain with tab isolation
- **Real-Time Monitoring**: 30-60 second interval monitoring with incremental processing
- **AI-Powered Analysis**: Advanced flash sale detection and pre-sale chat pattern recognition
- **Distributed State Management**: Redis-based locking with JSON state persistence

### Advanced Capabilities
- **Incremental Comment Processing**: Only process new comments since last check
- **Last-Page-First Strategy**: Start from newest comments for optimal performance
- **Thread-Safe Operations**: Concurrent task management with proper synchronization
- **Anti-Detection**: Rotating user agents, stealth mode, and request randomization
- **Automatic Cleanup**: Browser and tab lifecycle management based on task activity

## 📋 Requirements

### System Requirements
- Python 3.8+
- Chrome/Chromium browser
- Redis server (optional but recommended)
- 2GB+ RAM for optimal performance
- Network access to target forums

### Dependencies
See `task_system_requirements.txt` for complete list. Key dependencies:
- FastAPI + Uvicorn (REST API)
- DrissionPage (Browser automation)
- Redis (Distributed locking)
- SQLite (Task persistence)
- Pydantic (Data validation)

## 🛠️ Installation

### 1. Clone and Setup
```bash
git clone <repository>
cd web-crawling-task-system
```

### 2. Install Dependencies
```bash
pip install -r task_system_requirements.txt
```

### 3. Install Redis (Optional but Recommended)
```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# macOS
brew install redis

# Windows
# Download from https://redis.io/download
```

### 4. Configuration
Copy and customize the configuration:
```bash
cp task_system_config.json.template task_system_config.json
# Edit configuration as needed
```

## 🚀 Quick Start

### Start the System
```bash
python start_task_system.py
```

### Start with Custom Options
```bash
python start_task_system.py --host 0.0.0.0 --port 8000 --reload
```

### Access API Documentation
- Swagger UI: http://localhost:8000/docs
- ReDoc: http://localhost:8000/redoc

## 📡 API Usage

### Create a New Task
```bash
curl -X POST "http://localhost:8000/tasks" \
  -H "Content-Type: application/json" \
  -d '{
    "post_url": "https://lowendtalk.com/discussion/12345/example-post",
    "forum_domain": "lowendtalk.com",
    "monitor_interval": 45,
    "ai_analysis_enabled": true
  }'
```

### Start Task Monitoring
```bash
curl -X PUT "http://localhost:8000/tasks/{task_id}/start"
```

### List All Tasks
```bash
curl -X GET "http://localhost:8000/tasks"
```

### Stop All Tasks
```bash
curl -X POST "http://localhost:8000/tasks/stop-all"
```

## 🏗️ Architecture Overview

### Component Structure
```
┌─────────────────────────────────────────────────────────────────┐
│                    Task Management API                          │
│                     (FastAPI + REST)                           │
├─────────────────────────────────────────────────────────────────┤
│                  Task Execution Engine                         │
│              (Orchestrates task lifecycle)                     │
├─────────────────────────────────────────────────────────────────┤
│                Task Browser Manager                             │
│           (Domain-based browser grouping)                      │
├─────────────────────────────────────────────────────────────────┤
│                Enhanced AI Analyzer                             │
│        (Flash sale detection + content analysis)               │
├─────────────────────────────────────────────────────────────────┤
│     State Manager     │    Redis Locking    │   SQLite DB      │
│   (JSON persistence)  │   (Distributed)     │  (Task storage)  │
└─────────────────────────────────────────────────────────────────┘
```

### Browser Management Strategy
- **Domain Grouping**: Each forum domain gets one browser instance
- **Tab Isolation**: Each task gets its own tab within the domain browser
- **Lifecycle Management**: Browsers auto-cleanup when no tasks are active
- **Resource Optimization**: Shared browser instances reduce memory usage

### Task Execution Flow
1. **Task Creation**: API creates task record in SQLite database
2. **Browser Allocation**: Task gets assigned to domain-specific browser tab
3. **State Loading**: Load previous comment processing state from JSON
4. **Monitoring Loop**: Real-time monitoring with configurable intervals
5. **Incremental Processing**: Only process new comments since last check
6. **AI Analysis**: Analyze new comments for flash sales and patterns
7. **State Persistence**: Save updated state for recovery between restarts

## 🤖 AI Analysis Features

### Flash Sale Detection
- **Keyword Matching**: Configurable keyword patterns for offers/sales
- **Price Extraction**: Automatic price detection and parsing
- **Urgency Scoring**: Calculate urgency based on language patterns
- **Technical Specs**: Extract RAM, CPU, storage, bandwidth information

### Pre-Sale Chat Detection
- **Question Patterns**: Detect "when will this be available" type questions
- **Anticipation Keywords**: Identify excitement and waiting patterns
- **Timeline Requests**: Recognize ETA and availability inquiries

### Content Classification
- Flash Sale Announcements
- Pre-Sale Chat/Questions
- Price Discussions
- Availability Updates
- Technical Specifications
- General Discussion

## ⚙️ Configuration

### Key Configuration Sections

#### API Settings
```json
{
  "api": {
    "host": "0.0.0.0",
    "port": 8000,
    "title": "Web Crawling Task Management API"
  }
}
```

#### Browser Management
```json
{
  "browser_management": {
    "browser_idle_timeout": 300,
    "tab_idle_timeout": 180,
    "headless": true,
    "anti_detection": {
      "rotate_user_agents": true,
      "stealth_mode": true
    }
  }
}
```

#### Task Execution
```json
{
  "task_execution": {
    "default_monitor_interval": 45,
    "max_concurrent_tasks": 20,
    "retry_attempts": 3
  }
}
```

#### AI Analysis
```json
{
  "ai_analysis": {
    "enabled": true,
    "confidence_threshold": 0.3,
    "flash_sale_keywords": ["offer", "sale", "discount", ...]
  }
}
```

## 🔧 Advanced Usage

### Custom Forum Support
To add support for new forums, update the configuration:

```json
{
  "forum_specific": {
    "your_forum": {
      "domain": "yourforum.com",
      "comments_per_page": 20,
      "selectors": {
        "comment_element": "css:.comment",
        "comment_content": "css:.content"
      }
    }
  }
}
```

### Scaling Considerations
- **Horizontal Scaling**: Use Redis for distributed locking across instances
- **Resource Limits**: Configure max_concurrent_tasks based on system resources
- **Browser Optimization**: Adjust browser_idle_timeout for memory management
- **Database Optimization**: Consider PostgreSQL for high-volume deployments

## 🔍 Monitoring and Debugging

### Logging
- **Structured Logging**: JSON-formatted logs with thread information
- **Log Levels**: Configurable logging levels per component
- **File Rotation**: Automatic log file rotation and cleanup

### Health Monitoring
- **Task Status**: Real-time task status tracking
- **Browser Health**: Monitor browser and tab lifecycle
- **Performance Metrics**: Track processing times and success rates
- **Error Reporting**: Comprehensive error tracking and reporting

### Debug Mode
```bash
python start_task_system.py --reload --log-level debug
```

## 🚨 Troubleshooting

### Common Issues

#### Browser Crashes
- Check Chrome/Chromium installation
- Verify sufficient system memory
- Review anti-detection settings

#### Redis Connection Issues
- Ensure Redis server is running
- Check connection parameters in config
- System falls back to file-based locking

#### Task Failures
- Review task logs for specific errors
- Check forum accessibility and structure
- Verify selector patterns for target forum

#### Performance Issues
- Reduce max_concurrent_tasks
- Increase monitor_interval
- Enable headless mode
- Optimize browser cleanup settings

## 📈 Performance Optimization

### Memory Management
- Use headless browsers to reduce memory usage
- Configure appropriate idle timeouts
- Limit concurrent tasks based on system resources

### Network Optimization
- Implement request delays to avoid rate limiting
- Use connection pooling for HTTP requests
- Configure appropriate timeouts

### Processing Efficiency
- Last-page-first comment processing
- Incremental state management
- Efficient selector patterns
- Batch operations where possible

## 🔒 Security Considerations

### Anti-Detection
- Rotating user agents
- Random request delays
- Stealth browser mode
- Realistic browsing patterns

### Data Protection
- Secure state file storage
- Redis authentication (if enabled)
- API rate limiting
- Input validation and sanitization

## 📚 API Reference

### Task Model
```json
{
  "id": "uuid",
  "post_url": "string",
  "forum_domain": "string", 
  "monitor_interval": 45,
  "ai_analysis_enabled": true,
  "status": "pending|running|stopped|completed|error",
  "created_at": "datetime",
  "comment_count": 0,
  "flash_sales_found": 0
}
```

### Response Formats
All API responses follow consistent JSON format with appropriate HTTP status codes and error messages.

## 🤝 Contributing

1. Fork the repository
2. Create feature branch
3. Add tests for new functionality
4. Ensure all tests pass
5. Submit pull request

## 📄 License

This project is licensed under the MIT License - see LICENSE file for details.

## 🆘 Support

For issues and questions:
1. Check the troubleshooting section
2. Review logs for error details
3. Create GitHub issue with reproduction steps
4. Include system information and configuration
