# Task Management System Requirements
# Web Crawling Task Management API with Real-time Monitoring

# Core Web Framework
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
pydantic>=2.5.0

# Browser Automation
DrissionPage>=4.0.0
selenium>=4.15.0
undetected-chromedriver>=3.5.0

# Database and Storage
sqlite3  # Built into Python
redis>=5.0.0
redis-py-cluster>=2.1.0

# HTTP and Networking
httpx>=0.25.0
aiohttp>=3.9.0
requests>=2.31.0

# Data Processing and Analysis
pandas>=2.1.0
numpy>=1.24.0
beautifulsoup4>=4.12.0
lxml>=4.9.0

# Enhanced Logging and Monitoring
structlog>=23.2.0
loguru>=0.7.0
prometheus-client>=0.19.0

# Configuration Management
pydantic-settings>=2.1.0
python-dotenv>=1.0.0
PyYAML>=6.0.1

# Security and Cryptography
cryptography>=41.0.0
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0

# Async and Concurrency
asyncio  # Built into Python
aiofiles>=23.2.0
asyncpg>=0.29.0  # For future PostgreSQL support

# Testing Framework
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-mock>=3.12.0
httpx-test>=0.1.0

# Development Tools
black>=23.11.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.7.0

# Machine Learning and AI (Optional)
scikit-learn>=1.3.0
nltk>=3.8.0
textblob>=0.17.1
spacy>=3.7.0

# Performance and Optimization
cachetools>=5.3.0
memory-profiler>=0.61.0
psutil>=5.9.0

# Task Queue and Background Jobs
celery>=5.3.0
kombu>=5.3.0
billiard>=4.2.0

# API Documentation
python-multipart>=0.0.6  # For file uploads
jinja2>=3.1.0  # For templates

# Date and Time Handling
python-dateutil>=2.8.0
pytz>=2023.3

# JSON and Data Serialization
orjson>=3.9.0  # Fast JSON library
msgpack>=1.0.0

# Environment and System
python-dotenv>=1.0.0
click>=8.1.0  # For CLI commands

# Monitoring and Metrics
psutil>=5.9.0
py-cpuinfo>=9.0.0

# Optional: Enhanced Browser Features
playwright>=1.40.0  # Alternative to Selenium
pyautogui>=0.9.54  # For GUI automation if needed

# Optional: Database Alternatives
sqlalchemy>=2.0.0  # For advanced database operations
alembic>=1.13.0    # For database migrations

# Optional: Message Queue Alternatives
pika>=1.3.0        # For RabbitMQ
kafka-python>=2.0.0 # For Apache Kafka

# Optional: Distributed Computing
dask>=2023.11.0    # For distributed processing
ray>=2.8.0         # For distributed AI/ML

# Optional: Advanced Analytics
plotly>=5.17.0     # For data visualization
dash>=2.14.0       # For web dashboards

# Optional: Cloud Integration
boto3>=1.34.0      # For AWS integration
google-cloud-storage>=2.10.0  # For Google Cloud
azure-storage-blob>=12.19.0   # For Azure

# Development and Debugging
ipython>=8.17.0
jupyter>=1.0.0
debugpy>=1.8.0

# Documentation
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0

# Code Quality
pre-commit>=3.6.0
bandit>=1.7.0      # Security linting
safety>=2.3.0      # Security vulnerability checking
